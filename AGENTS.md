# Repository Guidelines

## 沟通约定
项目协作默认使用中文进行讨论与文档更新。

## Project Structure & Module Organization
The repository centers on the MetaTrader 4 Manager API artifacts. Root-level headers (`MT4ManagerAPI.h`, `MT4ManAPI.h`) expose the API surface, while the paired dynamic libraries (`mtmanapi.dll`, `mtmanapi64.dll`) deliver the runtime bindings for 32-bit and 64-bit clients. Reference material lives in `MT4ManagerAPI_Functions.md`, and connection defaults are stored in `config.ini`. When adding new samples or utilities, keep them in clearly named subdirectories at the root so the binary payloads stay uncluttered.

## Build, Test, and Development Commands
No formal build pipeline exists because the binaries ship precompiled. When validating header changes, compile against a minimal client stub, for example with `cl.exe /EHsc /I . sample.cpp mtmanapi64.lib`. Document any additional scripts you introduce under an `examples/` or `tools/` directory and provide invocation notes in their README files.

## 注释保留要求
每次修改或新增功能时，不得改动或删除仓库中已存在的注释代码段。

## Coding Style & Naming Conventions
Follow the existing Win32-oriented C interface style: PascalCase for exported functions, ALL_CAPS for constants, and snake_case for local helpers in ancillary utilities. Maintain 4-space indentation in C/C++ sources and wrap lines at 120 characters. Run `clang-format -i <file>` when touching headers, using the project-default LLVM style unless a local `.clang-format` is added.

## Testing Guidelines
There is no automated test suite today; every contribution must include a manual verification plan summarizing the trading scenarios exercised (login, trade retrieval, order placement). If you add harness code, mirror the naming pattern `test_<feature>.cpp` and provide a console entry point that exits non-zero on failure so it can be wired into CI later.

## Commit & Pull Request Guidelines
Craft concise commit subjects in imperative mood (e.g., "Document manager reconnection procedure") and include a short body explaining rationale and risk. Pull requests should list affected symbols, reference related tickets, and attach logs or screenshots showing successful connectivity to the configured MT4 server. Record any credential handling changes so operators can update `config.ini` securely.

## Security & Configuration Tips
Treat `config.ini` as sensitive; replace real credentials with environment variables or secrets management when sharing patches. Never commit new binaries without noting their origin and checksum. For troubleshooting, prefer redacting account identifiers rather than altering production data within the repository.

## Python Demo Extension Notes
- The root-level `mt4_manager_demo.py` drives all ctypes calls into the Manager vtable. When you add a new API, insert its virtual-table index into `_METHOD_INDEX`; the index matches the declaration order inside `CManagerInterface` and starts at 0.
- Mirror struct layouts from `MT4ManagerAPI.h` exactly when introducing new `ctypes.Structure` types. Use `_decode_char_array` for C char arrays and `_safe_text` before printing so Windows consoles that default to GBK do not throw encoding errors.
- Manager-allocated buffers must be released with `mem_free` once you finish reading them. Follow `_collect_groups` or `_collect_symbols` as templates to avoid leaks.
- Call the corresponding `*Refresh` helper (e.g., `SymbolsRefresh`) when the API requires a cache update. Treat `RET_OK_NONE` as success and surface other error codes through `_wrap_error` for consistent messaging.
- Run `python mt4_manager_demo.py` for validation; the script reads `config.ini` and autoselects the correct `mtmanapi*.dll`. The MT4 API may drop `symbols.raw` and `symgroups.raw` during execution; remove them afterwards if you want a clean working tree.



