﻿#!/usr/bin/env python3
"""MT4 Manager API login verification demo using ctypes."""
import argparse
import ctypes
import os
import sys
from pathlib import Path
from datetime import datetime

MAN_API_PROGRAM_VERSION = 400
MAN_API_PROGRAM_BUILD = 1260
MAN_API_VERSION = (MAN_API_PROGRAM_VERSION << 16) | MAN_API_PROGRAM_BUILD
WSA_VERSION = 0x0202
RET_OK = 0
RET_OK_NONE = 1
RET_INVALID_DATA = 3
_SUCCESS_CODES = {RET_OK, RET_OK_NONE}
TRADE_MODE_LABELS = {
    0: "disabled",
    1: "close-only",
    2: "full",
}
COLORREF = ctypes.c_uint32
TIME32 = ctypes.c_int32
MARGINLEVEL_OK = 0
MARGINLEVEL_MARGINCALL = 1
MARGINLEVEL_STOPOUT = 2
MARGIN_LEVEL_LABELS = {
    MARGINLEVEL_OK: "normal",
    MARGINLEVEL_MARGINCALL: "margin-call",
    MARGINLEVEL_STOPOUT: "stop-out",
}
MAX_SEC_GROUPS = 32
MAX_SEC_GROPS_MARGIN = 128


def _is_success(code: int) -> bool:
    return code in _SUCCESS_CODES


def _decode_char_array(buffer: ctypes.Array) -> str:
    raw = bytes(buffer)
    return raw.split(b"\x00", 1)[0].decode("utf-8", errors="ignore")


def _safe_text(value: str) -> str:
    if not value:
        return ""
    encoding = getattr(sys.stdout, "encoding", None) or "utf-8"
    return value.encode(encoding, errors="ignore").decode(encoding, errors="ignore")


def _format_time(value: int) -> str:
    if value <= 0:
        return ""
    try:
        return datetime.utcfromtimestamp(value).strftime("%Y-%m-%d %H:%M:%S")
    except (OverflowError, OSError, ValueError):
        return str(value)


def _format_ipv4(value: int) -> str:
    if value <= 0:
        return ""
    value &= 0xFFFFFFFF
    return ".".join(str((value >> shift) & 0xFF) for shift in (24, 16, 8, 0))


class ConGroupSec(ctypes.Structure):
    _fields_ = [
        ("show", ctypes.c_int),
        ("trade", ctypes.c_int),
        ("execution", ctypes.c_int),
        ("comm_base", ctypes.c_double),
        ("comm_type", ctypes.c_int),
        ("comm_lots", ctypes.c_int),
        ("comm_agent", ctypes.c_double),
        ("comm_agent_type", ctypes.c_int),
        ("spread_diff", ctypes.c_int),
        ("lot_min", ctypes.c_int),
        ("lot_max", ctypes.c_int),
        ("lot_step", ctypes.c_int),
        ("ie_deviation", ctypes.c_int),
        ("confirmation", ctypes.c_int),
        ("trade_rights", ctypes.c_int),
        ("ie_quick_mode", ctypes.c_int),
        ("autocloseout_mode", ctypes.c_int),
        ("comm_tax", ctypes.c_double),
        ("comm_agent_lots", ctypes.c_int),
        ("freemargin_mode", ctypes.c_int),
        ("reserved", ctypes.c_int * 3),
    ]


class ConGroupMargin(ctypes.Structure):
    _fields_ = [
        ("symbol", ctypes.c_char * 12),
        ("swap_long", ctypes.c_double),
        ("swap_short", ctypes.c_double),
        ("margin_divider", ctypes.c_double),
        ("reserved", ctypes.c_int * 7),
    ]


class ConGroup(ctypes.Structure):
    _fields_ = [
        ("group", ctypes.c_char * 16),
        ("enable", ctypes.c_int),
        ("timeout", ctypes.c_int),
        ("otp_mode", ctypes.c_int),
        ("company", ctypes.c_char * 128),
        ("signature", ctypes.c_char * 128),
        ("support_page", ctypes.c_char * 128),
        ("smtp_server", ctypes.c_char * 64),
        ("smtp_login", ctypes.c_char * 32),
        ("smtp_password", ctypes.c_char * 32),
        ("support_email", ctypes.c_char * 64),
        ("templates", ctypes.c_char * 32),
        ("copies", ctypes.c_int),
        ("reports", ctypes.c_int),
        ("default_leverage", ctypes.c_int),
        ("default_deposit", ctypes.c_double),
        ("maxsecurities", ctypes.c_int),
        ("secgroups", ConGroupSec * MAX_SEC_GROUPS),
        ("secmargins", ConGroupMargin * MAX_SEC_GROPS_MARGIN),
        ("secmargins_total", ctypes.c_int),
        ("currency", ctypes.c_char * 12),
        ("credit", ctypes.c_double),
        ("margin_call", ctypes.c_int),
        ("margin_mode", ctypes.c_int),
        ("margin_stopout", ctypes.c_int),
        ("interestrate", ctypes.c_double),
        ("use_swap", ctypes.c_int),
        ("news", ctypes.c_int),
        ("rights", ctypes.c_int),
        ("check_ie_prices", ctypes.c_int),
        ("maxpositions", ctypes.c_int),
        ("close_reopen", ctypes.c_int),
        ("hedge_prohibited", ctypes.c_int),
        ("close_fifo", ctypes.c_int),
        ("hedge_largeleg", ctypes.c_int),
        ("unused_rights", ctypes.c_int * 2),
        ("securities_hash", ctypes.c_char * 16),
        ("margin_type", ctypes.c_int),
        ("archive_period", ctypes.c_int),
        ("archive_max_balance", ctypes.c_int),
        ("stopout_skip_hedged", ctypes.c_int),
        ("archive_pending_period", ctypes.c_int),
        ("news_languages", ctypes.c_uint * 8),
        ("news_languages_total", ctypes.c_uint),
        ("reserved", ctypes.c_int * 17),
    ]


class ConSession(ctypes.Structure):
    _fields_ = [
        ("open_hour", ctypes.c_short),
        ("open_min", ctypes.c_short),
        ("close_hour", ctypes.c_short),
        ("close_min", ctypes.c_short),
        ("open", ctypes.c_int),
        ("close", ctypes.c_int),
        ("align", ctypes.c_short * 7),
    ]


class ConSessions(ctypes.Structure):
    _fields_ = [
        ("quote", ConSession * 3),
        ("trade", ConSession * 3),
        ("quote_overnight", ctypes.c_int),
        ("trade_overnight", ctypes.c_int),
        ("reserved", ctypes.c_int * 2),
    ]


class ConSymbol(ctypes.Structure):
    _fields_ = [
        ("symbol", ctypes.c_char * 12),
        ("description", ctypes.c_char * 64),
        ("source", ctypes.c_char * 12),
        ("currency", ctypes.c_char * 12),
        ("type", ctypes.c_int),
        ("digits", ctypes.c_int),
        ("trade", ctypes.c_int),
        ("background_color", COLORREF),
        ("count", ctypes.c_int),
        ("count_original", ctypes.c_int),
        ("external_unused", ctypes.c_int * 7),
        ("realtime", ctypes.c_int),
        ("starting", TIME32),
        ("expiration", TIME32),
        ("sessions", ConSessions * 7),
        ("profit_mode", ctypes.c_int),
        ("profit_reserved", ctypes.c_int),
        ("filter", ctypes.c_int),
        ("filter_counter", ctypes.c_int),
        ("filter_limit", ctypes.c_double),
        ("filter_smoothing", ctypes.c_int),
        ("filter_reserved", ctypes.c_float),
        ("logging", ctypes.c_int),
        ("spread", ctypes.c_int),
        ("spread_balance", ctypes.c_int),
        ("exemode", ctypes.c_int),
        ("swap_enable", ctypes.c_int),
        ("swap_type", ctypes.c_int),
        ("swap_long", ctypes.c_double),
        ("swap_short", ctypes.c_double),
        ("swap_rollover3days", ctypes.c_int),
        ("contract_size", ctypes.c_double),
        ("tick_value", ctypes.c_double),
        ("tick_size", ctypes.c_double),
        ("stops_level", ctypes.c_int),
        ("gtc_pendings", ctypes.c_int),
        ("margin_mode", ctypes.c_int),
        ("margin_initial", ctypes.c_double),
        ("margin_maintenance", ctypes.c_double),
        ("margin_hedged", ctypes.c_double),
        ("margin_divider", ctypes.c_double),
        ("point", ctypes.c_double),
        ("multiply", ctypes.c_double),
        ("bid_tickvalue", ctypes.c_double),
        ("ask_tickvalue", ctypes.c_double),
        ("long_only", ctypes.c_int),
        ("instant_max_volume", ctypes.c_int),
        ("margin_currency", ctypes.c_char * 12),
        ("freeze_level", ctypes.c_int),
        ("margin_hedged_strong", ctypes.c_int),
        ("value_date", TIME32),
        ("quotes_delay", ctypes.c_int),
        ("swap_openprice", ctypes.c_int),
        ("swap_variation_margin", ctypes.c_int),
        ("unused", ctypes.c_int * 21),
    ]


class MarginLevel(ctypes.Structure):
    _fields_ = [
        ("login", ctypes.c_int),
        ("group", ctypes.c_char * 16),
        ("leverage", ctypes.c_int),
        ("updated", ctypes.c_int),
        ("balance", ctypes.c_double),
        ("equity", ctypes.c_double),
        ("volume", ctypes.c_int),
        ("margin", ctypes.c_double),
        ("margin_free", ctypes.c_double),
        ("margin_level", ctypes.c_double),
        ("margin_type", ctypes.c_int),
        ("level_type", ctypes.c_int),
    ]


class UserRecord(ctypes.Structure):
    _fields_ = [
        ("login", ctypes.c_int),
        ("group", ctypes.c_char * 16),
        ("password", ctypes.c_char * 16),
        ("enable", ctypes.c_int),
        ("enable_change_password", ctypes.c_int),
        ("enable_read_only", ctypes.c_int),
        ("enable_otp", ctypes.c_int),
        ("enable_reserved", ctypes.c_int * 2),
        ("password_investor", ctypes.c_char * 16),
        ("password_phone", ctypes.c_char * 32),
        ("name", ctypes.c_char * 128),
        ("country", ctypes.c_char * 32),
        ("city", ctypes.c_char * 32),
        ("state", ctypes.c_char * 32),
        ("zipcode", ctypes.c_char * 16),
        ("address", ctypes.c_char * 96),
        ("lead_source", ctypes.c_char * 32),
        ("phone", ctypes.c_char * 32),
        ("email", ctypes.c_char * 48),
        ("comment", ctypes.c_char * 64),
        ("id", ctypes.c_char * 32),
        ("status", ctypes.c_char * 16),
        ("regdate", TIME32),
        ("lastdate", TIME32),
        ("leverage", ctypes.c_int),
        ("agent_account", ctypes.c_int),
        ("timestamp", TIME32),
        ("last_ip", ctypes.c_int),
        ("balance", ctypes.c_double),
        ("prevmonthbalance", ctypes.c_double),
        ("prevbalance", ctypes.c_double),
        ("credit", ctypes.c_double),
        ("interestrate", ctypes.c_double),
        ("taxes", ctypes.c_double),
        ("prevmonthequity", ctypes.c_double),
        ("prevequity", ctypes.c_double),
        ("reserved2", ctypes.c_double * 2),
        ("otp_secret", ctypes.c_char * 32),
        ("secure_reserved", ctypes.c_char * 240),
        ("send_reports", ctypes.c_int),
        ("mqid", ctypes.c_uint),
        ("user_color", COLORREF),
        ("unused", ctypes.c_char * 40),
        ("api_data", ctypes.c_char * 16),
    ]


class MT4ManagerError(Exception):
    """Raised when the MT4 Manager API reports an error."""


class MT4Manager:
    """Minimal ctypes wrapper for the MT4 Manager interface."""

    _METHOD_INDEX = {
        "Release": 2,
        "MemFree": 3,
        "ErrorDescription": 4,
        "WorkingDirectory": 5,
        "Connect": 6,
        "Disconnect": 7,
        "IsConnected": 8,
        "Login": 9,
        "Ping": 12,
        "CfgRequestGroup": 27,
        "SymbolsRefresh": 85,
        "SymbolsGetAll": 86,
        "GroupsRequest": 93,
        "UsersRequest": 97,
        "GroupsGet": 115,
        "UsersGet": 118,
        "UserRecordGet": 119,
        "MarginLevelRequest": 156,
    }

    def __init__(self, dll_path: Path):
        self._dll = ctypes.WinDLL(str(dll_path))
        self._ptr = ctypes.c_void_p()
        self._mtman_version = self._dll.MtManVersion
        self._mtman_version.argtypes = ()
        self._mtman_version.restype = ctypes.c_int
        self._reported_version = self._mtman_version()
        mtman_create = self._dll.MtManCreate
        mtman_create.argtypes = (ctypes.c_int, ctypes.POINTER(ctypes.c_void_p))
        mtman_create.restype = ctypes.c_int
        requested_version = (
            MAN_API_VERSION
            if self._reported_version == MAN_API_VERSION
            else self._reported_version
        )
        result = mtman_create(requested_version, ctypes.byref(self._ptr))
        if not _is_success(result) or not self._ptr.value:
            raise MT4ManagerError(
                f"MtManCreate failed with code {result} (DLL version {self._reported_version}, requested {requested_version})"
            )
        self._negotiated_version = requested_version
        self._vtable = None
        self._func_cache = {}

    def api_version(self) -> int:
        return self._reported_version

    def negotiated_version(self) -> int:
        return self._negotiated_version

    def _ensure_vtable(self) -> None:
        if self._vtable is None:
            obj_ptr = ctypes.cast(
                self._ptr, ctypes.POINTER(ctypes.POINTER(ctypes.c_void_p))
            )
            self._vtable = ctypes.cast(
                obj_ptr.contents, ctypes.POINTER(ctypes.c_void_p)
            )

    def _get_method(self, name: str, restype, argtypes):
        self._ensure_vtable()
        func = self._func_cache.get(name)
        if func is None:
            index = self._METHOD_INDEX[name]
            prototype = ctypes.WINFUNCTYPE(restype, ctypes.c_void_p, *argtypes)
            address = self._vtable[index]
            func = prototype(address)
            self._func_cache[name] = func
        return func

    def mem_free(self, ptr) -> None:
        if ptr is None:
            return
        value = ptr.value if isinstance(ptr, ctypes.c_void_p) else ptr
        if not value:
            return
        func = self._get_method("MemFree", None, (ctypes.c_void_p,))
        func(self._ptr, ctypes.c_void_p(value))

    def working_directory(self, path: str) -> None:
        func = self._get_method("WorkingDirectory", None, (ctypes.c_char_p,))
        func(self._ptr, os.fsencode(path))

    def connect(self, server: str) -> None:
        func = self._get_method("Connect", ctypes.c_int, (ctypes.c_char_p,))
        result = func(self._ptr, os.fsencode(server))
        if not _is_success(result):
            raise self._wrap_error(result, "Connect")

    def login(self, login: int, password: str) -> None:
        func = self._get_method("Login", ctypes.c_int, (ctypes.c_int, ctypes.c_char_p))
        result = func(self._ptr, int(login), os.fsencode(password))
        if not _is_success(result):
            raise self._wrap_error(result, "Login")

    def ping(self) -> None:
        func = self._get_method("Ping", ctypes.c_int, ())
        result = func(self._ptr)
        if not _is_success(result):
            raise self._wrap_error(result, "Ping")

    def disconnect(self) -> None:
        func = self._get_method("Disconnect", ctypes.c_int, ())
        func(self._ptr)

    def is_connected(self) -> bool:
        func = self._get_method("IsConnected", ctypes.c_int, ())
        return bool(func(self._ptr))

    def release(self) -> None:
        if self._ptr and self._ptr.value:
            func = self._get_method("Release", ctypes.c_int, ())
            func(self._ptr)
            self._ptr.value = 0

    def error_description(self, code: int) -> str:
        func = self._get_method("ErrorDescription", ctypes.c_char_p, (ctypes.c_int,))
        message = func(self._ptr, int(code))
        return message.decode("utf-8", errors="ignore") if message else "Unknown error"

    def _wrap_error(self, code: int, action: str) -> MT4ManagerError:
        description = self.error_description(code)
        return MT4ManagerError(f"{action} failed ({code}): {description}")

    def _collect_groups(self, method_name: str):
        func = self._get_method(
            method_name, ctypes.c_void_p, (ctypes.POINTER(ctypes.c_int),)
        )
        total = ctypes.c_int(0)
        raw_ptr = func(self._ptr, ctypes.byref(total))
        if not raw_ptr:
            return [], total.value
        data_ptr = ctypes.c_void_p(raw_ptr)
        groups = []
        try:
            array = ctypes.cast(data_ptr, ctypes.POINTER(ConGroup))
            count = max(0, total.value)
            for idx in range(count):
                record = array[idx]
                groups.append(
                    {
                        "name": _decode_char_array(record.group),
                        "enabled": bool(record.enable),
                        "currency": _decode_char_array(record.currency),
                        "margin_call": record.margin_call,
                        "margin_stopout": record.margin_stopout,
                        "max_positions": record.maxpositions,
                    }
                )
        finally:
            self.mem_free(data_ptr)
        return groups, total.value

    def refresh_symbols(self) -> None:
        func = self._get_method("SymbolsRefresh", ctypes.c_int, ())
        result = func(self._ptr)
        if not _is_success(result):
            raise self._wrap_error(result, "SymbolsRefresh")

    def _collect_symbols(self):
        func = self._get_method(
            "SymbolsGetAll", ctypes.c_void_p, (ctypes.POINTER(ctypes.c_int),)
        )
        total = ctypes.c_int(0)
        raw_ptr = func(self._ptr, ctypes.byref(total))
        if not raw_ptr:
            return [], total.value
        data_ptr = ctypes.c_void_p(raw_ptr)
        symbols = []
        try:
            array = ctypes.cast(data_ptr, ctypes.POINTER(ConSymbol))
            count = max(0, total.value)
            for idx in range(count):
                record = array[idx]
                name = _decode_char_array(record.symbol)
                if not name:
                    continue
                symbols.append(
                    {
                        "name": name,
                        "description": _decode_char_array(record.description),
                        "currency": _decode_char_array(record.currency),
                        "digits": record.digits,
                        "trade_mode": TRADE_MODE_LABELS.get(
                            record.trade, f"unknown({record.trade})"
                        ),
                        "contract_size": record.contract_size,
                    }
                )
        finally:
            self.mem_free(data_ptr)
        return symbols, total.value

    def _collect_users(self, method_name: str):
        func = self._get_method(
            method_name, ctypes.c_void_p, (ctypes.POINTER(ctypes.c_int),)
        )
        total = ctypes.c_int(0)
        raw_ptr = func(self._ptr, ctypes.byref(total))
        if not raw_ptr:
            return [], total.value
        data_ptr = ctypes.c_void_p(raw_ptr)
        users = []
        try:
            array = ctypes.cast(data_ptr, ctypes.POINTER(UserRecord))
            count = max(0, total.value)
            for idx in range(count):
                record = array[idx]
                users.append(
                    {
                        "login": record.login,
                        "group": _safe_text(_decode_char_array(record.group)),
                        "name": _safe_text(_decode_char_array(record.name)),
                        "email": _safe_text(_decode_char_array(record.email)),
                        "phone": _safe_text(_decode_char_array(record.phone)),
                        "country": _safe_text(_decode_char_array(record.country)),
                        "city": _safe_text(_decode_char_array(record.city)),
                        "address": _safe_text(_decode_char_array(record.address)),
                        "status": _safe_text(_decode_char_array(record.status)),
                        "comment": _safe_text(_decode_char_array(record.comment)),
                        "balance": record.balance,
                        "credit": record.credit,
                        "leverage": record.leverage,
                        "last_login": _format_time(record.lastdate),
                        "last_ip": _format_ipv4(record.last_ip),
                        "agent_account": record.agent_account,
                        "send_reports": bool(record.send_reports),
                        "enabled": bool(record.enable),
                    }
                )
        finally:
            self.mem_free(data_ptr)
        return users, total.value

    def _user_record_get(self, login: int):
        func = self._get_method(
            "UserRecordGet", ctypes.c_int, (ctypes.c_int, ctypes.POINTER(UserRecord))
        )
        record = UserRecord()
        result = func(self._ptr, int(login), ctypes.byref(record))
        if not _is_success(result):
            if result == RET_INVALID_DATA:
                return None
            raise self._wrap_error(result, "UserRecordGet")
        return {
            "login": record.login,
            "group": _safe_text(_decode_char_array(record.group)),
            "name": _safe_text(_decode_char_array(record.name)),
            "email": _safe_text(_decode_char_array(record.email)),
            "phone": _safe_text(_decode_char_array(record.phone)),
            "country": _safe_text(_decode_char_array(record.country)),
            "city": _safe_text(_decode_char_array(record.city)),
            "address": _safe_text(_decode_char_array(record.address)),
            "status": _safe_text(_decode_char_array(record.status)),
            "comment": _safe_text(_decode_char_array(record.comment)),
            "balance": record.balance,
            "credit": record.credit,
            "leverage": record.leverage,
            "last_login": _format_time(record.lastdate),
            "last_ip": _format_ipv4(record.last_ip),
            "agent_account": record.agent_account,
            "send_reports": bool(record.send_reports),
        }

    def margin_level_request(self, login: int):
        func = self._get_method(
            "MarginLevelRequest",
            ctypes.c_int,
            (ctypes.c_int, ctypes.POINTER(MarginLevel)),
        )
        level = MarginLevel()
        result = func(self._ptr, int(login), ctypes.byref(level))
        if not _is_success(result):
            if result == RET_INVALID_DATA:
                return None
            raise self._wrap_error(result, "MarginLevelRequest")
        return {
            "login": level.login,
            "group": _safe_text(_decode_char_array(level.group)),
            "leverage": level.leverage,
            "updated": bool(level.updated),
            "balance": level.balance,
            "equity": level.equity,
            "volume": level.volume,
            "margin": level.margin,
            "margin_free": level.margin_free,
            "margin_level": level.margin_level,
            "margin_type": level.margin_type,
            "level_type": level.level_type,
        }

    def list_groups(self):
        for method in ("GroupsRequest", "GroupsGet", "CfgRequestGroup"):
            groups, _ = self._collect_groups(method)
            if groups:
                return groups
        return []

    def list_symbols(self):
        try:
            self.refresh_symbols()
        except MT4ManagerError:
            pass
        symbols, _ = self._collect_symbols()
        symbols.sort(key=lambda item: item["name"])
        return symbols

    def list_users(self):
        for method in ("UsersGet", "UsersRequest"):
            users, _ = self._collect_users(method)
            if users:
                users.sort(key=lambda item: item["login"])
                return users
        return []

    def close(self) -> None:
        try:
            if self.is_connected():
                self.disconnect()
        finally:
            self.release()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


def winsock_startup() -> ctypes.WinDLL:
    ws2_32 = ctypes.WinDLL("ws2_32.dll")
    wsadata = ctypes.create_string_buffer(400)
    ws2_32.WSAStartup.argtypes = (ctypes.c_ushort, ctypes.c_void_p)
    ws2_32.WSAStartup.restype = ctypes.c_int
    result = ws2_32.WSAStartup(WSA_VERSION, ctypes.byref(wsadata))
    if result != 0:
        raise MT4ManagerError(f"WSAStartup failed with code {result}")
    return ws2_32


def winsock_cleanup(ws2_32: ctypes.WinDLL) -> None:
    if ws2_32 is not None:
        ws2_32.WSACleanup.argtypes = ()
        ws2_32.WSACleanup.restype = ctypes.c_int
        ws2_32.WSACleanup()


def load_credentials(config_path: Path) -> dict:
    data = {}
    with config_path.open("r", encoding="utf-8") as handle:
        for raw_line in handle:
            line = raw_line.strip()
            if not line or line.startswith("#"):
                continue
            if "=" not in line:
                continue
            key, value = line.split("=", 1)
            data[key.strip().upper()] = value.strip()
    try:
        server = data["MT4_SERVER_ADDRESS"]
        login = int(data["MT4_LOGIN"])
        password = data["MT4_PWD"]
    except KeyError as missing:
        raise MT4ManagerError(
            f"Missing required key in config.ini: {missing}"
        ) from None
    except ValueError as exc:
        raise MT4ManagerError(f"MT4_LOGIN must be an integer: {exc}") from None
    return {"server": server, "login": login, "password": password}


def resolve_path(base: Path, candidate: Path) -> Path:
    return candidate if candidate.is_absolute() else (base / candidate)


def report_user_funds(manager: MT4Manager, login: int) -> None:
    user = None
    detail_error = None
    try:
        users = manager.list_users()
    except MT4ManagerError as exc:
        detail_error = exc
    else:
        if users:
            user = next((entry for entry in users if entry.get("login") == login), None)
    if user is None:
        try:
            user = manager._user_record_get(login)
        except MT4ManagerError as exc:
            if detail_error is None:
                detail_error = exc
    level = manager.margin_level_request(login)
    if level is None and user is None:
        message = f"Account {login}: no account data returned."
        if detail_error is not None:
            message += f" ({detail_error})"
        print(message)
        return
    print(f"Account {login} funds overview:")
    if user is not None:
        print(f"    Balance: {user.get('balance', 0.0):.2f}")
        print(f"    Credit: {user.get('credit', 0.0):.2f}")
    elif detail_error is not None:
        print(f"    User record unavailable: {detail_error}")
    if level is not None:
        group = _safe_text(level.get("group") or "-")
        status = MARGIN_LEVEL_LABELS.get(level.get("level_type"), "unknown")
        print(f"    Group: {group}  Leverage: {level.get('leverage', 0)}")
        print(f"    Margin requirement: {level.get('margin', 0.0):.2f}")
        print(f"    Equity: {level.get('equity', 0.0):.2f}")
        print(f"    Free margin: {level.get('margin_free', 0.0):.2f}")
        margin_level = level.get("margin_level")
        if margin_level is not None:
            print(f"    Margin level: {margin_level:.2f} ({status})")
    else:
        print("    Margin data not available.")


def main(argv=None) -> int:
    if os.name != "nt":
        print("This demo can only be executed on Windows.", file=sys.stderr)
        return 1
    base_dir = Path(__file__).resolve().parent
    is_64bit = sys.maxsize > 2**32
    default_dll = base_dir / ("mtmanapi64.dll" if is_64bit else "mtmanapi.dll")
    parser = argparse.ArgumentParser(description="MT4 Manager connection demo")
    parser.add_argument(
        "--config",
        type=Path,
        default=base_dir / "config.ini",
        help="Path to config.ini with MT4 credentials",
    )
    parser.add_argument(
        "--dll",
        type=Path,
        default=default_dll,
        help="Path to the MT4 Manager API DLL to load",
    )
    args = parser.parse_args(argv)
    config_path = resolve_path(base_dir, args.config).resolve()
    dll_path = resolve_path(base_dir, args.dll).resolve()
    if not config_path.exists():
        print(f"Config file not found: {config_path}", file=sys.stderr)
        return 1
    if not dll_path.exists():
        print(f"DLL not found at: {dll_path}", file=sys.stderr)
        return 1
    credentials = load_credentials(config_path)
    ws2_32 = None
    try:
        ws2_32 = winsock_startup()
        with MT4Manager(dll_path) as manager:
            manager.working_directory(str(base_dir))
            api_version = manager.api_version()
            if api_version != MAN_API_VERSION:
                print(
                    f"Warning: expected API version {MAN_API_VERSION} but DLL reported {api_version}; "
                    f"negotiated {manager.negotiated_version()}"
                )
            manager.connect(credentials["server"])
            manager.login(credentials["login"], credentials["password"])
            manager.ping()
            print(
                f"Login succeeded for {credentials['login']} @ {credentials['server']}"
            )
          
            # 1.查询用户组
            # groups = manager.list_groups()
            # if groups:
            #     print("Managed groups:")
            #     for group in groups:
            #         status = "enabled" if group["enabled"] else "disabled"
            #         print(
            #             f"  - {group['name']} ({status}) currency={group['currency']} "
            #             f"margin_call={group['margin_call']} stopout={group['margin_stopout']} "
            #             f"max_positions={group['max_positions']}"
            #         )
            # else:
            #     print("Managed groups: none reported by server.")
            # 2.查询交易品种信息
            # symbols = manager.list_symbols()
            # if symbols:
            #     print(f"Available symbols ({len(symbols)}):")
            #     for symbol in symbols:
            #         name = _safe_text(symbol['name'])
            #         desc = _safe_text(symbol['description'])
            #         currency = _safe_text(symbol['currency'])
            #         desc_suffix = f" - {desc}" if desc else ""
            #         print(
            #             f"  - {name} ({symbol['trade_mode']}) digits={symbol['digits']} "
            #             f"contract={symbol['contract_size']:.2f} currency={currency}{desc_suffix}"
            #         )
            # else:
            #     print("Available symbols: none reported by server.")
            # 3.查询单个用户信息
            # users_s = manager.list_users()
            # target_login = ********
            # target = next((entry for entry in users_s if entry['login'] == target_login), None)
            # detail_error = None
            # if target is None:
            #     try:
            #         target = manager._user_record_get(target_login)
            #     except MT4ManagerError as exc:
            #         detail_error = exc
            # if target:
            #     print("Detailed user view:")
            #     print(
            #         f"    login={target['login']} group={_safe_text(target.get('group', '-'))} "
            #         f"name={_safe_text(target.get('name', '-'))}"
            #     )
            #     print(
            #         f"    balance={target.get('balance', 0.0):.2f} credit={target.get('credit', 0.0):.2f} "
            #         f"leverage={target.get('leverage', 0)}"
            #     )
            #     print(
            #         f"    last_login={target.get('last_login') or '-'} ip={target.get('last_ip') or '-'}"
            #     )
            #     print(
            #         f"    email={_safe_text(target.get('email', '-'))} phone={_safe_text(target.get('phone', '-'))}"
            #     )
            #     print(
            #         f"    country={_safe_text(target.get('country', '-'))} "
            #         f"city={_safe_text(target.get('city', '-'))}"
            #     )
            #     print(
            #         f"    address={_safe_text(target.get('address', '-'))} "
            #         f"status={_safe_text(target.get('status', '-'))}"
            #     )
            #     print(
            #         f"    comment={_safe_text(target.get('comment', '-'))} "
            #         f"agent_account={target.get('agent_account', '-')} "
            #         f"send_reports={target.get('send_reports', False)}"
            #     )
            # elif detail_error is not None:
            #     print(f"Detailed user view: query failed for {target_login}: {detail_error}")
            # else:
            #     print("Detailed user view: no record returned for ********")
            # 4.查询所有交易用户信息
            # users = manager.list_users()
            # if users:
            #     print(f"Trading users ({len(users)}):")
            #     for user in users:
            #         name = _safe_text(user['name'])
            #         email = _safe_text(user['email'])
            #         group = _safe_text(user['group'])
            #         status = "enabled" if user['enabled'] else "disabled"
            #         last_login = user['last_login'] or "-"
            #         last_ip = user['last_ip'] or "-"
            #         email_part = f" email={email}" if email else ""
            #         print(
            #             f"  - {user['login']} ({status}) group={group} "
            #             f"balance={user['balance']:.2f} credit={user['credit']:.2f} "
            #             f"leverage={user['leverage']} last_login={last_login} ip={last_ip}{email_part}"
            #         )
            # else:
            #     print("Trading users: none reported by server.")

            # 5.查询用户资金情况
            # report_user_funds(manager, ********)

            
    except MT4ManagerError as exc:
        print(f"[MT4ManagerError] {exc}", file=sys.stderr)
        return 2
    except OSError as exc:
        print(f"[OSError] {exc}", file=sys.stderr)
        return 3
    finally:
        winsock_cleanup(ws2_32)
    return 0


if __name__ == "__main__":
    raise SystemExit(main())
