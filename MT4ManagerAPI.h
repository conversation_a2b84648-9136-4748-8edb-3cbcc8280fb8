//+------------------------------------------------------------------+
//|                                         MetaTrader 4 Manager API |
//|                   Copyright 2001-2018, MetaQuotes Software Corp. |
//|                                        http://www.metaquotes.net |
//+------------------------------------------------------------------+
//|                   DO NOT EDIT THIS FILE!!!                       |
//+------------------------------------------------------------------+
// NoStyler
#pragma once
//+------------------------------------------------------------------+
//|                        API Version                               |
//+------------------------------------------------------------------+
#define ManAPIProgramVersion  400
#define ManAPIProgramBuild    1170
#define ManAPIVersion         MAKELONG(ManAPIProgramBuild,ManAPIProgramVersion)
//+------------------------------------------------------------------+
//| MSVS6 Compatibility                                              |
//+------------------------------------------------------------------+
#if _MSC_VER <= 1200
#define __time32_t time_t
#define __ptr32
#endif
//+------------------------------------------------------------------+
//|                   Server Configurations                          |
//+------------------------------------------------------------------+
//| Configuration types                                              |
//+------------------------------------------------------------------+
enum
{
	CONFIG_ALL = 0,                             // all configurations
	CONFIG_COMMON,                            // common configuration
	CONFIG_ACCESS,                            // IP access list configuration
	CONFIG_SERVERS,                           // data servers
	CONFIG_TIME,                              // working time configuration
	CONFIG_HOLIDAYS,                          // holidays configuration
	CONFIG_SYMBOLS,                           // symbols configuration
	CONFIG_SYMB_GROUPS,                       // securities configuration
	CONFIG_GROUPS,                            // groups configuration
	CONFIG_MANAGERS,                          // manager's rights configuration
	CONFIG_DATAFEEDS,                         // data feeds configuration
	CONFIG_BACKUP,                            // backups configuration
	CONFIG_LIVEUPDATE,                        // LiveUpdate configuration
	CONFIG_SYNC,                              // synchronization configuration
	CONFIG_PLUGINS,                           // plugins configuration
	CONFIG_GATEWAY_ACCOUNTS,                  // gateway accounts
	CONFIG_GATEWAY_MARKUPS,                   // gateway markups
	CONFIG_GATEWAY_RULES,                     // gateway rules
	CONFIG_END = 255                            // last (unused)
};
//+------------------------------------------------------------------+
//| Configuration structures                                         |
//+------------------------------------------------------------------+
//| Common configuration                                             |
//+------------------------------------------------------------------+
struct ConCommon
{
	char              owner[128];            // servers owner (include version & build)
	char              name[32];              // server name
	ULONG             address;               // IP address assigned to the server
	int               port;                  // port
	DWORD             timeout;               // sockets timeout
	int               typeofdemo;            // demo-accounts type (DEMO_DISABLED, DEMO_PROLONG, DEMO_FIXED)
	int               timeofdemo;            // demo-account living time
	int               daylightcorrection;    // allow daylight correction
	char              internal[60];          // reserved
	int               timezone_real;         // time zone with day light mode
	int               timezone;              // time zone 0-GMT;-1=GMT-1;1=GMT+1;
	char              timesync[64];          // time synchronization server address
	//---
	int               minclient;             // minimal authorized client version
	int               minapi;                // minimal authorized client version
	DWORD             feeder_timeout;        // data feed switch timeout
	int               keepemails;            // internal mail keep period
	int               endhour, endminute;     // end of day time-hour & minute
	//---
	int               optimization_time;     // optimization start time (minutes)
	int               optimization_lasttime; // optimization last time
	int               optimization_counter;  // internal variable
	int               optimization_unused[8];// reserved for future use
	//---
	int               antiflood;             // enable antiflood control
	int               floodcontrol;          // max. antiflood connections
	//---
	int               liveupdate_mode;       // LiveUpdate mode (LIVE_UPDATE_NO,LIVE_UPDATE_ALL,LIVE_UPDATE_NO_SERVER)
	//---
	int               lastorder;             // last order's ticket     (read only)
	int               lastlogin;             // last account's number   (read only)
	int               lostlogin;             // lost commission's login (read only)
	//---
	int               rollovers_mode;        // rollover mode (ROLLOVER_NORMAL,ROLLOVER_REOPEN_BY_CLOSE_PRICE,ROLLOVER_REOPEN_BY_BID)
	//---
	char              path_database[256];    // path to databases
	char              path_history[256];     // path to history bases
	char              path_log[256];         // path to log
	//--- overnigths
	__time32_t        overnight_last_day;    // day of last overnight
	__time32_t        overnight_last_time;   // time of last overnight
	__time32_t        overnight_prev_time;   // time of next to last overnight
	//--- month reports
	__time32_t        overmonth_last_month;  // month of last report
	//--- performance base
	char              adapters[256];         // network adapters list (read-only)
	ULONG             bind_adresses[8];      // array of avaible IP addresses
	short             server_version;        // server version
	short             server_build;          // server build
	ULONG             web_adresses[8];       // web services access list (comma separated IP addresses)
	int               statement_mode;        // statement generation time (STATEMENT_END_DAY,STATEMENT_START_DAY)
	int               monthly_state_mode;    // monthly statement generation day (MONTHLY_STATEMENT_END_MONTH,MONTHLY_STATEMENT_START_MONTH)
	int               keepticks;             // ticks keep period
	int               statement_weekend;     // generate statements at weekends
	__time32_t        last_activate;         // last activation datetime
	__time32_t        stop_last;             // last stop datetime
	int               stop_delay;            // last stop delay
	int               stop_reason;           // last stop reason
	char              account_url[128];      // account allocation URL
	int               reserved[16];
};
//--- deno-accounts type
enum { DEMO_DISABLED, DEMO_PROLONG, DEMO_FIXED };
//--- rollover mode
enum { ROLLOVER_NORMAL, ROLLOVER_REOPEN_BY_CLOSE_PRICE, ROLLOVER_REOPEN_BY_BID };
//--- LiveUpdate mode
enum { LIVE_UPDATE_NO = 0, LIVE_UPDATE_RELEASE = 1, LIVE_UPDATE_NO_SERVER = 2, LIVE_UPDATE_BETA = 3 };
//--- statement mode
enum { STATEMENT_END_DAY, STATEMENT_START_DAY };
//--- monthly statement mode
enum { MONTHLY_STATEMENT_END_MONTH, MONTHLY_STATEMENT_START_MONTH };
//--- server stop reason
enum { STOP_REASON_NONE, STOP_REASON_RESTART, STOP_REASON_SHUTDOWN, STOP_REASON_LIVEUPDATE };
//+------------------------------------------------------------------+
//| Access configuration                                             |
//+------------------------------------------------------------------+
struct ConAccess
{
	int               action;                // type of action (FW_BLOCK,FW_PERMIT)
	unsigned long     from, to;               // from, to addresses
	char              comment[64];           // comment
	int               reserved[17];          // reserved
};
//--- access action
enum { FW_BLOCK, FW_PERMIT };
//+------------------------------------------------------------------+
//| Data Servers configuration                                       |
//+------------------------------------------------------------------+
struct ConDataServer
{
	char              server[64];             // server address (server:ip)
	ULONG             ip;                     // server IP
	char              description[64];        // server description
	int               isproxy;                // can server be proxy?
	int               priority;               // priority: 0-7 base, 255-idle
	UINT              loading;                // server loading (UINT_MAX-server does not inform its loading)
	ULONG             ip_internal;            // internal IP address
	int               reserved[2];            // reserved
	ConDataServer* __ptr32 next;                   // internal (not used)
};
//+------------------------------------------------------------------+
//| Time configuration                                               |
//+------------------------------------------------------------------+
struct ConTime
{
	int               days[7][24];           // server's accessebility (7 days-24 hours, 0-denied, 1-allowed)
	int               dayscontrol;           // internal variable
	int               reserved[3];           // reserved
};
//+------------------------------------------------------------------+
//| Backup configuration                                             |
//+------------------------------------------------------------------+
struct ConBackup
{
	//---
	char              fullbackup_path[256];   // path to backup
	int               fullbackup_period;      // full backup's period-BACKUP_1HOUR, BACKUP_4HOURS, BACKUP_1DAY
	int               fullbackup_store;       // full backup's store time-BU_STORE_1MONTH, BU_STORE_3MONTHS, BU_STORE_6MONTHS,BU_STORE_1YEAR
	__time32_t        fullbackup_lasttime;    // full backup's last execution time
	short             fullbackup_shift;       // full backup timeshift (minutes)
	//---
	char              external_path[256];     // path to external processing directory
	//---
	int               archive_period;         // period of archive backup-ARC_BACKUP_5MIN, ARC_BACKUP_15MIN, ARC_BACKUP_30MIN, ARC_BACKUP_1HOUR
	int               archive_store;          // archive backup's store time-ARC_STORE_1WEEK, ARC_STORE_2WEEKS, ARC_STORE_1MONTH, ARC_STORE_3MONTH, ARC_STORE_6MONTH
	__time32_t        archive_lasttime;       // archive backup's last execution time
	//---
	char              export_securities[512]; // comma separated list of exported securities
	char              export_path[256];       // path to export script
	int               export_period;          // export period-enumeration EXPORT_1MIN, EXPORT_5MIN, EXPORT_15MIN, EXPORT_30MIN,EXPORT_1HOUR
	__time32_t        export_lasttime;        // export's last execution time
	//--- watch dog
	int               watch_role;             // server role { WATCH_STAND_ALONE, WATCH_MASTER, WATCH_SLAVE }
	char              watch_password[16];     // slave server password
	char              watch_opposite[24];     // opposite server IP address and port
	int               watch_ip;               // opposite server IP
	//--- 
	char              archive_shift;          // shift of archive backup time (in minutes)
	//---
	char              watch_state;            // watch dog state
	char              watch_failover;         // watch dog failover mode
	unsigned char     watch_timeout;          // watch dog timeout
	int               watch_login;            // watch dog login
	__time32_t        watch_timestamp;        // watch dog timestamp
};
//--- server role
enum { WATCH_STAND_ALONE, WATCH_MASTER, WATCH_SLAVE };
//--- full backup execution periods: 1 hour, 4 hours, 1 day
enum { BACKUP_1HOUR, BACKUP_4HOURS, BACKUP_1DAY };
//--- full backup store period: 1 month, 3 months, 6 months, 1 year
enum { BU_STORE_1MONTH, BU_STORE_3MONTHS, BU_STORE_6MONTHS, BU_STORE_1YEAR };
//--- arc. backup execution periods: 5 min, 15 min, 30 min, 1 hour
enum { ARC_BACKUP_DISABLED, ARC_BACKUP_5MIN, ARC_BACKUP_15MIN, ARC_BACKUP_30MIN, ARC_BACKUP_1HOUR };
//--- arc. backup store period: 1 day, 3 days, 1 week, 2 weeks, 1 month,3 months, 6 months,1 year
enum { ARC_STORE_1DAY, ARC_STORE_3DAYS, ARC_STORE_1WEEK, ARC_STORE_2WEEKS, ARC_STORE_1MONTH, ARC_STORE_3MONTH, ARC_STORE_6MONTH };
//--- export execution period: 1 min, 5 min, 15 min, 30 min, 1 hour
enum { EXPORT_1MIN, EXPORT_5MIN, EXPORT_15MIN, EXPORT_30MIN, EXPORT_1HOUR };
//--- watchdog state
enum { WS_DISCONNECTED, WS_SYNCHRONIZING, WS_SYNCHRONIZED };
//--- watchdog failover mode
enum { FAILOVER_OFF, FAILOVER_MOST, FAILOVER_FULL };
//+------------------------------------------------------------------+
//| Datafeed configuration                                           |
//+------------------------------------------------------------------+
struct ConFeeder
{
	char              name[64];              // name
	char              file[256];             // datafeed filename
	char              server[64];            // server address
	char              login[32];             // datafeed login
	char              pass[32];              // datafeed password
	char              keywords[256];         // keywords (news filtration)
	int               enable;                // enable feeder
	int               mode;                  // datafeed mode-enumeration FEED_QUOTES, FEED_NEWS, FEED_QUOTESNEWS
	int               timeout;               // max. freeze time (default ~120 sec.)
	int               timeout_reconnect;     // reconnect timeout before attemps_sleep connect attempts (default ~ 5  sec)
	int               timeout_sleep;         // reconnect timeout after attemps_sleep connect attempts  (default ~ 60 sec)
	int               attemps_sleep;         // reconnect count (see timeout_reconnect & timeout_sleep)
	int               news_langid;           // news language id
	int               unused[33];            // reserved
};
//--- datafeed modes-receive quotes, receive news, receive quotes and news
enum { FEED_QUOTES = 0, FEED_NEWS = 1, FEED_QUOTESNEWS = 2 };
//+------------------------------------------------------------------+
//| Security group configuration for client group                    |
//+------------------------------------------------------------------+
#define MAX_SEC_GROUPS       (32)
#define MAX_SEC_GROPS_MARGIN (128)
//---
struct ConGroupSec
{
	int               show, trade;            // enable show and trade for this group of securites
	int               execution;             // dealing mode-EXECUTION_MANUAL,EXECUTION_AUTO,EXECUTION_ACTIVITY
	//--- comission settings
	double            comm_base;             // standart commission
	int               comm_type;             // commission type-COMM_TYPE_MONEY,COMM_TYPE_PIPS,COMM_TYPE_PERCENT
	int               comm_lots;             // commission lots mode-COMMISSION_PER_LOT,COMMISSION_PER_DEAL
	double            comm_agent;            // agent commission
	int               comm_agent_type;       // agent commission mode-COMM_TYPE_MONEY, COMM_TYPE_PIPS
	//---
	int               spread_diff;           // spread difference in compare with default security spread
	//---
	int               lot_min, lot_max;       // allowed minimal and maximal lot values
	int               lot_step;              // allowed step value (10 lot-1000, 1 lot-100, 0.1 lot-10)
	int               ie_deviation;          // maximum price deviation in Instant Execution mode
	int               confirmation;          // use confirmation in Request mode
	int               trade_rights;          // clients trade rights-bit mask see TRADE_DENY_NONE,TRADE_DENY_CLOSEBY,TRADE_DENY_MUCLOSEBY
	int               ie_quick_mode;         // do not resend request to the dealer when client uses deviation
	int               autocloseout_mode;     // auto close-out method { CLOSE_OUT_NONE, CLOSE_OUT_HIHI, CLOSE_OUT_LOLO, CLOSE_OUT_HILO, CLOSE_OUT_LOHI, CLOSE_OUT_LOHI, CLOSE_OUT_FIFO, CLOSE_OUT_LIFO, CLOSE_OUT_INTRDAY_FIFO }
	double            comm_tax;              // commission taxes
	int               comm_agent_lots;       // agent commission per lot/per deal { COMMISSION_PER_LOT,COMMISSION_PER_DEAL }
	int               freemargin_mode;       // "soft" margin check
	int               reserved[3];           // reserved
};
//+------------------------------------------------------------------+
//| Special securities configurations for client group               |
//+------------------------------------------------------------------+
struct ConGroupMargin
{
	char              symbol[12];            // security
	double            swap_long, swap_short;  // swap size for long and short positions
	double            margin_divider;        // margin divider
	int               reserved[7];
};
//--- dealing mode
enum { EXECUTION_MANUAL, EXECUTION_AUTO, EXECUTION_ACTIVITY };
//--- commission type
enum { COMM_TYPE_MONEY, COMM_TYPE_PIPS, COMM_TYPE_PERCENT };
//--- comission lots mode
enum { COMMISSION_PER_LOT, COMMISSION_PER_DEAL };
//--- clients trade rights
enum { TRADE_DENY_NONE = 0, TRADE_DENY_CLOSEBY = 1, TRADE_DENY_MUCLOSEBY = 2 };
//--- auto close-out method
enum { CLOSE_OUT_NONE, CLOSE_OUT_HIHI, CLOSE_OUT_LOLO, CLOSE_OUT_HILO, CLOSE_OUT_LOHI, CLOSE_OUT_FIFO, CLOSE_OUT_LIFO, CLOSE_OUT_INTRDAY_FIFO };
//+------------------------------------------------------------------+
//| Client group configuration                                       |
//+------------------------------------------------------------------+
struct ConGroup
{
	//--- common settings
	char              group[16];                   // group name
	int               enable;                      // enable group
	int               timeout;                     // trade confirmation timeout (seconds)
	int               otp_mode;                    // one-time password mode
	//--- statements
	char              company[128];                // company name
	char              signature[128];              // statements signature
	char              support_page[128];           // company support page
	char              smtp_server[64];             // statements SMTP server
	char              smtp_login[32];              // statements SMTP login
	char              smtp_password[32];           // statements SMTP password
	char              support_email[64];           // support email
	char              templates[32];               // path to directory with custom templates
	int               copies;                      // copy statements on support email
	int               reports;                     // enable statements
	//--- default settings
	int               default_leverage;            // default leverage (user don't specify leverage himself)
	double            default_deposit;             // default deposit  (user don't specify balance  himself)
	//--- securities
	int               maxsecurities;               // maximum simultaneous securities
	ConGroupSec       secgroups[MAX_SEC_GROUPS];   // security group settings
	ConGroupMargin    secmargins[MAX_SEC_GROPS_MARGIN]; // special securities settings
	int               secmargins_total;            // count of special securities settings
	//--- margin & interest
	char              currency[12];                // deposit currency
	double            credit;                      // virtual credit
	int               margin_call;                 // margin call level (percents)
	int               margin_mode;                 // margin mode-MARGIN_DONT_USE,MARGIN_USE_ALL,MARGIN_USE_PROFIT,MARGIN_USE_LOSS
	int               margin_stopout;              // stop out level
	double            interestrate;                // annual interest rate (percents)
	int               use_swap;                    // use rollovers & interestrate
	//--- rights
	int               news;                        // news mode
	int               rights;                      // rights bit mask-ALLOW_FLAG_EMAIL
	int               check_ie_prices;             // check IE prices on requests
	int               maxpositions;                // maximum orders and open positions
	int               close_reopen;                // partial close mode (if !=0 original position will be fully closed and remain position will be fully reopened)
	int               hedge_prohibited;            // hedge prohibition flag
	int               close_fifo;                  // fifo rule 
	int               hedge_largeleg;              // use large leg margin for hedged positions
	int               unused_rights[2];            // reserved

	char              securities_hash[16];         // internal data
	//---
	int               margin_type;                 // margin controlling type { MARGIN_TYPE_PERCENT,  MARGIN_TYPE_CURRENCY }
	//--- archives
	int               archive_period;              // inactivity period after which account moves to archive base (in days)
	int               archive_max_balance;         // maxumum balance of accounts to move in archive base
	//---
	int               stopout_skip_hedged;         // skip fully hedged accounts when checking for stopout
	int               archive_pending_period;      // pendings clean period
	//--- allowed news languages
	UINT              news_languages[8];           // LANGID array
	UINT              news_languages_total;        // news languages total
	//--- reserved
	int               reserved[17];
};
//--- margin calculation mode
enum { MARGIN_MODE_DONT_USE, MARGIN_MODE_USE_ALL, MARGIN_MODE_USE_PROFIT, MARGIN_MODE_USE_LOSS };
//--- margin controlling type
enum { MARGIN_TYPE_PERCENT, MARGIN_TYPE_CURRENCY };
//--- news mode-no news, only topics, full news (topic+body)
enum { NEWS_NO, NEWS_TOPICS, NEWS_FULL };
//--- group rights
enum
{
	ALLOW_FLAG_EMAIL = 1,
	ALLOW_FLAG_TRAILING = 2,
	ALLOW_FLAG_ADVISOR = 4,
	ALLOW_FLAG_EXPIRATION = 8,
	ALLOW_FLAG_SIGNALS_ALL = 16,
	ALLOW_FLAG_SIGNALS_OWN = 32,
	ALLOW_FLAG_RISK_WARNING = 64,
	ALLOW_FLAG_FORCED_OTP_USAGE = 128,
};
//--- group one-time password mode
enum
{
	OTP_MODE_DISABLED = 0,
	OTP_MODE_TOTP_SHA256 = 1,
};
//+------------------------------------------------------------------+
//| Hollidays configuration                                          |
//+------------------------------------------------------------------+
struct ConHoliday
{
	int               year;                        // year
	int               month;                       // month
	int               day;                         // day
	int               from, to;                     // work time-from & to (minutes)
	char              symbol[32];                  // security name or symbol's group name or "All"
	char              description[128];            // description
	int               enable;                      // enable
	int               reserved[13];                // reserved
	ConHoliday* __ptr32 next;                     // internal data
};
//+------------------------------------------------------------------+
//| LiveUpdate configuration                                         |
//+------------------------------------------------------------------+
#define LIVE_FILES_MAX (128)
//---
struct LiveInfoFile
{
	char              file[256];                   // file name
	int               size;                        // file size
	char              hash[36];                    // file hash
	int               reserved[10];                // reserved
};
//---
struct ConLiveUpdate
{
	char              company[128];                // company
	char              path[256];                   // path to LiveUpdate
	int               version;                     // version
	int               build;                       // build
	int               maxconnect;                  // max. simultaneous connections
	int               connections;                 // current connections (read only)
	int               type;                        // type LIVE_UPDATE_*
	int               enable;                      // enable
	int               totalfiles;                  // total files count
	LiveInfoFile      files[LIVE_FILES_MAX];       // files' configurations
	int               reserved[16];                // reserved
	ConLiveUpdate* __ptr32 next;                        // internal data
};
//--- LiveUpdate type
enum
{
	LIVE_UPDATE_CLIENT,
	LIVE_UPDATE_MANAGER,
	LIVE_UPDATE_ADMIN,
	LIVE_UPDATE_DATACENTER,
	LIVE_UPDATE_CLIENT_PPC2002,
	LIVE_UPDATE_CLIENT_PPC2003,
	LIVE_UPDATE_MULTI,
	LIVE_UPDATE_WD,
	LIVE_UPDATE_CLIENT_PHONE,
	LIVE_UPDATE_LAST
};
//+------------------------------------------------------------------+
//| Manager rights for security groups                               |
//+------------------------------------------------------------------+
struct ConManagerSec
{
	int               internal;                    // internal data
	int               enable;                      // enable
	int               minimum_lots;                // min. lots
	int               maximum_lots;                // max. lots
	int               unused[16];                  // reserved
};
//+------------------------------------------------------------------+
//| Manager configuration                                            |
//+------------------------------------------------------------------+
struct ConManager
{
	int               login;                       // login
	//--- rights
	int               manager;                     // right to add & change client records
	int               money;                       // right to balance & credit management
	int               online;                      // right to see online users
	int               riskman;                     // right to use analyzer
	int               broker;                      // right to deal
	int               admin;                       // right to server administration
	int               logs;                        // right to see logs
	int               reports;                     // right to see reports
	int               trades;                      // right to add/modify/delete trades
	int               market_watch;                // right to change spread, spread balance, stop levels, execution mode and send quotes
	int               email;                       // right to send internal mail
	int               user_details;                // right to see clients private data-name,country,address,phone,email etc.
	int               see_trades;                  // right to see trades
	int               news;                        // right to send news
	int               plugins;                     // right to configure plugins
	int               server_reports;              // right to receive server reports
	int               techsupport;                 // right to access to technical support page
	int               market;                      // right to access server applications market
	int               notifications;               // right to push notifications
	int               unused[9];
	//--- IP filtration
	int               ipfilter;                    // enable IP control
	unsigned long     ip_from, ip_to;               // range of allowed IPs
	//---
	char              mailbox[64];                 // name of mailbox for internal mail
	char              groups[1024];                // comma separated list of managed groups (allowed '*' wildcard)
	ConManagerSec     secgroups[MAX_SEC_GROUPS];   // manager rights for security groups
	DWORD             exp_time;                    // internal data
	char              name[32];                    // manager name (read only)
	int               info_depth;                  // maximum available data (in days) 
	//---
	int               reserved[22];
};
//+------------------------------------------------------------------+
//| Symbol configurations                                            |
//+------------------------------------------------------------------+
//| Symbol sessions configurations                                   |
//+------------------------------------------------------------------+
struct ConSession
{
	short             open_hour, open_min;          // session open  time: hour & minute
	short             close_hour, close_min;        // session close time: hour & minute
	int               open, close;                  // internal data
	short             align[7];                    // internal data
};
//---
struct ConSessions
{
	//---
	ConSession        quote[3];                    // quote sessions
	ConSession        trade[3];                    // trade sessions
	//---
	int               quote_overnight;             // internal data
	int               trade_overnight;             // internal data
	int               reserved[2];                 // reserved
};
//+------------------------------------------------------------------+
//| Symbol configuration                                             |
//+------------------------------------------------------------------+
#define MAX_SYMBOLS 1024
//---
struct ConSymbol
{
	//--- common settings
	char              symbol[12];                  // name
	char              description[64];             // description
	char              source[12];                  // synonym
	char              currency[12];                // currency
	int               type;                        // security group (see ConSymbolGroup)
	int               digits;                      // security precision
	int               trade;                       // trade mode
	//--- external settings
	COLORREF          background_color;            // background color
	int               count;                       // symbols index
	int               count_original;              // symbols index in market watch
	int               external_unused[7];
	//--- sessions
	int               realtime;                    // allow real time quotes
	__time32_t        starting;                    // trades starting date (UNIX time)
	__time32_t        expiration;                  // trades end date      (UNIX time)
	ConSessions       sessions[7];                 // quote & trade sessions
	//--- profits
	int               profit_mode;                 // profit calculation mode
	int               profit_reserved;             // reserved
	//--- filtration
	int               filter;                      // filter value
	int               filter_counter;              // filtration parameter
	double            filter_limit;                // max. permissible deviation from last quote (percents)
	int               filter_smoothing;            // smoothing
	float             filter_reserved;             // reserved
	int               logging;                     // enable to log quotes
	//--- spread & swaps
	int               spread;                      // spread
	int               spread_balance;              // spread balance
	int               exemode;                     // execution mode
	int               swap_enable;                 // enable swaps
	int               swap_type;                   // swap type
	double            swap_long, swap_short;        // swaps values for long & short postions
	int               swap_rollover3days;          // triple rollover day-0-Monday,1-Tuesday...4-Friday
	double            contract_size;               // contract size
	double            tick_value;                  // one tick value
	double            tick_size;                   // one tick size
	int               stops_level;                 // stops deviation value
	//---
	int               gtc_pendings;                // GTC mode { ORDERS_DAILY, ORDERS_GTC, ORDERS_DAILY_NO_STOPS }
	//--- margin calculation
	int               margin_mode;                 // margin calculation mode
	double            margin_initial;              // initial margin
	double            margin_maintenance;          // margin maintenance
	double            margin_hedged;               // hedged margin
	double            margin_divider;              // margin divider
	//--- calclulated variables (internal data)
	double            point;                       // point size-(1/(10^digits)
	double            multiply;                    // multiply 10^digits
	double            bid_tickvalue;               // tickvalue for bid
	double            ask_tickvalue;               // tickvalue for ask
	//---
	int               long_only;                   // allow only BUY positions
	int               instant_max_volume;          // max. volume for Instant Execution
	//---
	char              margin_currency[12];         // currency of margin requirments
	int               freeze_level;                // modification freeze level
	int               margin_hedged_strong;        // strong hedged margin mode
	__time32_t        value_date;                  // value date
	int               quotes_delay;                // quotes delay after session start
	int               swap_openprice;         	  // use open price at swaps calculation in SWAP_BY_INTEREST mode
	int               swap_variation_margin;       // charge variation margin on rollover
	//---
	int               unused[21];             	  // reserved

};
//+------------------------------------------------------------------+
//| Symbols enumeration                                              |
//+------------------------------------------------------------------+
//--- symbol execution mode
enum { EXE_REQUEST, EXE_INSTANT, EXE_MARKET };
//--- trade mode
enum { TRADE_NO, TRADE_CLOSE, TRADE_FULL };
//--- swap type
enum { SWAP_BY_POINTS, SWAP_BY_DOLLARS, SWAP_BY_INTEREST, SWAP_BY_MARGIN_CURRENCY };
//--- profit calculation mode
enum { PROFIT_CALC_FOREX, PROFIT_CALC_CFD, PROFIT_CALC_FUTURES };
//--- margin calculation mode
enum { MARGIN_CALC_FOREX, MARGIN_CALC_CFD, MARGIN_CALC_FUTURES, MARGIN_CALC_CFDINDEX, MARGIN_CALC_CFDLEVERAGE };
//--- GTC mode
enum { ORDERS_DAILY, ORDERS_GTC, ORDERS_DAILY_NO_STOPS };
//+------------------------------------------------------------------+
//| Symbol groups                                                    |
//+------------------------------------------------------------------+
#define MAX_SEC_GROUP (32)
struct ConSymbolGroup
{
	char              name[16];                     // group name
	char              description[64];              // group description
};
//+------------------------------------------------------------------+
//| Synchronization configuration                                    |
//+------------------------------------------------------------------+
struct ConSync
{
	char              server[64];                   // name (address
	int               unusedport;                   // port
	char              login[32];                    // for future use-login
	char              password[32];                 // for future use=password
	int               enable;                       // enable sychronization
	int               mode;                         // synchronization mode: HB_ADD,HB_UPDATE,HB_INSERT
	__time32_t        from, to;                      // synchronization range (<0-whole chart)
	char              securities[1024];             // symbols list
	int               timecorrection;               // time correction in minutes
	int               reserved[13];                 // reserved
	ConSync* __ptr32 next;                         // internal (do not use)
};
//--- synchronization mode
enum { HB_ADD, HB_UPDATE, HB_INSERT, HB_DELETE, HB_LAST };
//+------------------------------------------------------------------+
//| Plugin configuration                                             |
//+------------------------------------------------------------------+
//| Plugin description                                               |
//+------------------------------------------------------------------+
struct PluginInfo
{
	char              name[128];                    // plugin name
	unsigned int      version;                      // plugin version
	char              copyright[128];               // plugin copyright
	int               reserved[32];                 // reserved
};
//+------------------------------------------------------------------+
//| Plugin configuration parameter                                   |
//+------------------------------------------------------------------+
struct PluginCfg
{
	char              name[32];                     // parameter name
	char              value[128];                   // parameter value
	int               reserved[16];                 // reserved
};
//+------------------------------------------------------------------+
//| Plugin configuration                                             |
//+------------------------------------------------------------------+
struct ConPlugin
{
	char              file[256];                    // plugin file name
	PluginInfo        info;                         // plugin description
	int               enabled;                      // plugin enabled/disabled
	int               configurable;                 // is plugin configurable
	int               manager_access;               // plugin can be accessed from manager terminal
	int               reserved[62];                 // reserved
};
//--- plugin with parameters
struct ConPluginParam
{
	ConPlugin         plugin;                       // plugin configuration
	PluginCfg* __ptr32 params;                      // plugin parameters
	int               total;                        // total plugin parameters
};
//+------------------------------------------------------------------+
//| Gateway configuration                                            |
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| Gateway account configuration                                    |
//+------------------------------------------------------------------+
struct ConGatewayAccount
{
	int               enable;                       // enable flag 0 - disabled, 1 - enabled
	char              name[64];                     // public name
	int               id;                           // internal id
	int               type;                         // type (obsolete)
	int               login;                        // STP MT4 login
	char              address[64];                  // MT4 server address
	char              password[64];                 // STP MT4 password
	int               notify_logins[8];             // list of logins for internal email notification
	int               flags;                        // flag fields
	int               reserved[23];                 // reserved
};
//--- gateway account flags
enum EnGatewayAccountFlags { GATEWAY_FLAG_NONE = 0, GATEWAY_FLAG_QUOTES = 1 };
//+------------------------------------------------------------------+
//| Gateway markup configuration                                     |
//+------------------------------------------------------------------+
struct ConGatewayMarkup
{
	int               enable;                       // enable flag 0 - disabled, 1 - enabled
	char              source[128];                  // source symbol\symbols mask\symbols group name
	char              symbol[12];                   // local symbol name
	char              account_name[64];             // account name (obsolete)
	int               account_id;                   // account internal id (obsolete)
	int               bid_markup;                   // bid markup in pips
	int               ask_markup;                   // ask markup in pips
	int               reserved[16];                 // reserved
};
//+------------------------------------------------------------------+
//| Gateway rules configuration                                     |
//+------------------------------------------------------------------+
struct ConGatewayRule
{
	int               enable;                       // enable flag 0 - disabled, 1 - enabled
	char              name[64];                     // public name
	char              request_symbol[128];          // symbol\symbols mask\symbols group name
	char              request_group[128];           // group name or group mask
	int               request_reserved[32];         // reserved
	char              exe_account_name[64];         // account name
	int               exe_account_id;               // account internal id
	int               exe_max_deviation;            // max. devation
	int               exe_max_profit_slippage;      // max profit slippage in pips
	int               exe_max_profit_slippage_lots; // max profit slippage volume in lots
	int               exe_max_losing_slippage;      // max losing slippage in pips
	int               exe_max_losing_slippage_lots; // max losing slippage volume in lots
	int               exe_account_pos;              // account current position
	int               exe_volume_percent;           // coverage percentage
	int               exe_reserved[26];             // reserved
};
//+------------------------------------------------------------------+
//|                           Result codes                           |
//+------------------------------------------------------------------+
enum
{
	//--- common errors
	RET_OK = 0,        // all OK
	RET_OK_NONE,                     // all OK-no operation
	RET_ERROR,                       // general error
	RET_INVALID_DATA,                // invalid data
	RET_TECH_PROBLEM,                // server technical problem
	RET_OLD_VERSION,                 // old client terminal
	RET_NO_CONNECT,                  // no connection
	RET_NOT_ENOUGH_RIGHTS,           // no enough rights
	RET_TOO_FREQUENT,                // too frequently access to server
	RET_MALFUNCTION,                 // mulfunctional operation
	RET_GENERATE_KEY,                // need to send public key
	RET_SECURITY_SESSION,            // security session start
	//--- account status
	RET_ACCOUNT_DISABLED = 64,       // account blocked
	RET_BAD_ACCOUNT_INFO,            // bad account info
	RET_PUBLIC_KEY_MISSING,          // public key missing on external auth
	//--- trade
	RET_TRADE_TIMEOUT = 128,      // trade transatcion timeou expired
	RET_TRADE_BAD_PRICES,            // order has wrong prices
	RET_TRADE_BAD_STOPS,             // wrong stops level
	RET_TRADE_BAD_VOLUME,            // wrong lot size
	RET_TRADE_MARKET_CLOSED,         // market closed
	RET_TRADE_DISABLE,               // trade disabled
	RET_TRADE_NO_MONEY,              // no enough money for order execution
	RET_TRADE_PRICE_CHANGED,         // price changed
	RET_TRADE_OFFQUOTES,             // no quotes
	RET_TRADE_BROKER_BUSY,           // broker is busy
	RET_TRADE_REQUOTE,               // requote
	RET_TRADE_ORDER_LOCKED,          // order is proceed by dealer and cannot be changed
	RET_TRADE_LONG_ONLY,             // allowed only BUY orders
	RET_TRADE_TOO_MANY_REQ,          // too many requests from one client
	//--- order status notification
	RET_TRADE_ACCEPTED,              // trade request accepted by server and placed in request queue
	RET_TRADE_PROCESS,               // trade request accepted by dealerd
	RET_TRADE_USER_CANCEL,           // trade request canceled by client
	//--- additional return codes
	RET_TRADE_MODIFY_DENIED,         // trade modification denied
	RET_TRADE_CONTEXT_BUSY,          // trade context is busy (used in client terminal)
	RET_TRADE_EXPIRATION_DENIED,     // using expiration date denied
	RET_TRADE_TOO_MANY_ORDERS,       // too many orders
	RET_TRADE_HEDGE_PROHIBITED,      // hedge is prohibited
	RET_TRADE_PROHIBITED_BY_FIFO     // prohibited by fifo rule
};
//+------------------------------------------------------------------+
//| Pumping mode flags                                               |
//+------------------------------------------------------------------+
enum
{
	//--- user flags
	CLIENT_FLAGS_HIDETICKS = 1,     // do not send ticks
	CLIENT_FLAGS_HIDENEWS = 2,     // do not send news
	CLIENT_FLAGS_HIDEMAIL = 4,     // do not send mails
	CLIENT_FLAGS_SENDFULLNEWS = 8,     // send news body with news header in pumping mode
	CLIENT_FLAGS_RESERVED = 16,    // reserved
	//--- manager flags
	CLIENT_FLAGS_HIDEONLINE = 32,    // do not send online users table
	CLIENT_FLAGS_HIDEUSERS = 64     // do not send users table
};
//+------------------------------------------------------------------+
//|  Server datafeed descritopn                                      |
//+------------------------------------------------------------------+
struct FeedDescription
{
	int               version;                   // data source version
	char              name[128];                 // data source name
	char              copyright[128];            // copyright string
	char              web[128];                  // data source web
	char              email[128];                // data source email
	char              server[128];               // feeder server
	char              username[32];              // default feeder name
	char              userpass[32];              // default feeder password
	int               modes;                     // feeder modes (enum FeederModes)
	char              description[512];          // feeder description
	char              module[32];                // datafeed name in license
	int               reserved[54];              // reserved
};
//--- feeder modes
enum FeederModes
{
	modeOnlyQuotes = 0, // only quotes feeder
	modeOnlyNews = 1, // only news feeder
	modeQuotesAndNews = 2, // quotes and news feeder
	modeQuotesOrNews = 3  // quotes or news feeder
};
//--- server datafeed
struct ServerFeed
{
	char              file[256];                 // feeder file name
	FeedDescription   feed;                      // feeder description
};
//+------------------------------------------------------------------+
//|                           Charts                                 |
//+------------------------------------------------------------------+
//| Request chart history struct                                     |
//+------------------------------------------------------------------+
struct ChartInfo
{
	char              symbol[12];          // symbol
	int               period;              // period (PERIOD_*)
	__time32_t        start;               // start of chart block
	__time32_t        end;                 // end of chart block
	__time32_t        timesign;            // timestamp of existing chart base
	int               mode;                // request mode
};
//--- chart period
enum {
	PERIOD_M1 = 1, PERIOD_M5 = 5, PERIOD_M15 = 15, PERIOD_M30 = 30,
	PERIOD_H1 = 60, PERIOD_H4 = 240, PERIOD_D1 = 1440, PERIOD_W1 = 10080,
	PERIOD_MN1 = 43200
};
//--- request mode
enum { CHART_RANGE_IN, CHART_RANGE_OUT, CHART_RANGE_LAST };
//+------------------------------------------------------------------+
//| Rate the in chart base                                           |
//+------------------------------------------------------------------+
#pragma pack(push,1)
struct RateInfoOld
{
	__time32_t        ctm;                    // rate time
	int               open;                   // open price: 11987=119.87
	short             high, low, close;         // high,low,close shift from open
	double            vol;                    // volume
};
struct RateInfo
{
	__time32_t        ctm;                    // rate time
	int               open;                   // open price: 11987=119.87
	int               high, low, close;         // high,low,close shift from open
	double            vol;                    // volume
};
#pragma pack(pop)
//+------------------------------------------------------------------+
//| Tick record in base                                              |
//+------------------------------------------------------------------+
#pragma pack(push,1)
struct TickRecord
{
	__time32_t        ctm;                    // tick time
	double            bid, ask;                // bid, ask
	int               datafeed;               // index if datafeed
	char              flags;                  // TICK_FLAG_* flags
};
#pragma pack(pop)
//---
enum { TICK_FLAG_RAW = 1, TICK_FLAG_NORMAL = 2, TICK_FLAG_ALL = TICK_FLAG_RAW + TICK_FLAG_NORMAL };
//+------------------------------------------------------------------+
//| Tick request                                                     |
//+------------------------------------------------------------------+
#pragma pack(push,1)
struct TickRequest
{
	char              symbol[12];             // symbol
	__time32_t        from;                   // start of period
	__time32_t        to;                     // end of period
	char              flags;                  // TICK_FLAG_* flags
};
#pragma pack(pop)
//+------------------------------------------------------------------+
//| Performance information                                          |
//+------------------------------------------------------------------+
#pragma pack(push,1)
struct PerformanceInfo
{
	__time32_t        ctm;
	short             users;                  // online users
	short             cpu;                    // processor loading (%)
	int               freemem;                // free memory (Kb)
	int               network;                // network activity (Kb/s)
	int               sockets;                // all open sockets in system
};
#pragma pack(pop)
//+------------------------------------------------------------------+
//| Backup file information                                          |
//+------------------------------------------------------------------+
struct BackupInfo
{
	char              file[256];              // file name
	int               size;                   // file size
	__time32_t        time;                   // file time
	int               reserved[6];            // reserved
};
//--- backup mode
enum
{
	BACKUPS_ALL,             // all backup
	BACKUPS_PERIODICAL,      // periodical backups
	BACKUPS_STARTUP,         // backups on startup
	BACKUPS_DELETE           // backups on delete
};
//+------------------------------------------------------------------+
//|                        Databases                                 |
//+------------------------------------------------------------------+
//| Transaction types                                                |
//+------------------------------------------------------------------+
enum { TRANS_ADD, TRANS_DELETE, TRANS_UPDATE, TRANS_CHANGE_GRP };
//+------------------------------------------------------------------+
//| User Record                                                      |
//+------------------------------------------------------------------+
#define PUBLIC_KEY_SIZE    272                  // RSA key size // (((1024+64)/32)*4*2)
#define USER_COLOR_NONE    (0xFF000000)         // default user color
//---
struct UserRecord
{
	//--- common settings
	int               login;                      // login
	char              group[16];                  // group
	char              password[16];               // password
	//--- access flags
	int               enable;                     // enable
	int               enable_change_password;     // allow to change password
	int               enable_read_only;           // allow to open/positions (TRUE-may not trade)
	int               enable_otp;                 // allow to use one-time password
	int               enable_reserved[2];         // for future use
	//---
	char              password_investor[16];      // read-only mode password
	char              password_phone[32];         // phone password
	char              name[128];                  // name
	char              country[32];                // country
	char              city[32];                   // city
	char              state[32];                  // state
	char              zipcode[16];                // zipcode
	char              address[96];                // address
	char              lead_source[32];            // lead source
	char              phone[32];                  // phone
	char              email[48];                  // email
	char              comment[64];                // comment
	char              id[32];                     // SSN (IRD)
	char              status[16];                 // status
	__time32_t        regdate;                    // registration date
	__time32_t        lastdate;                   // last coonection time
	//--- trade settings
	int               leverage;                   // leverage
	int               agent_account;              // agent account
	__time32_t        timestamp;                  // timestamp
	int               last_ip;                    // last visit ip
	//--- trade data
	double            balance;                    // balance
	double            prevmonthbalance;           // previous month balance
	double            prevbalance;                // previous day balance
	double            credit;                     // credit
	double            interestrate;               // accumulated interest rate
	double            taxes;                      // taxes
	double            prevmonthequity;            // previous month equity
	double            prevequity;                 // previous day equity
	double            reserved2[2];               // for future use
	//---
	char              otp_secret[32];             // one-time password secret
	char              secure_reserved[240];       // secure data reserved
	int               send_reports;               // enable send reports by email
	unsigned int      mqid;                       // MQ client identificator
	COLORREF          user_color;                 // color got to client (used by MT Manager)
	//---
	char              unused[40];                 // for future use
	char              api_data[16];               // for API usage
};
//+------------------------------------------------------------------+
//| Users group operation                                            |
//+------------------------------------------------------------------+
#pragma pack(push,1)
struct GroupCommandInfo
{
	int               len;              // length of users list
	char              command;          // group coommand
	char              newgroup[16];     // new group
	int               leverage;         // new leverage
	int               reserved[8];      // reserved
};
#pragma pack(pop)
//--- group commands
enum { GROUP_DELETE, GROUP_ENABLE, GROUP_DISABLE, GROUP_LEVERAGE, GROUP_SETGROUP };
//+------------------------------------------------------------------+
//| Online user description                                          |
//+------------------------------------------------------------------+
struct OnlineRecord
{
	int               counter;       // connections counter
	int               reserved;      // reserved
	int               login;         // user login
	UINT              ip;            // connection ip address
	char              group[16];     // user group
};
//+------------------------------------------------------------------+
//| Trade Record                                                     |
//+------------------------------------------------------------------+
#pragma pack(push,1)
struct TradeRecord
{
	int               order;            // order ticket
	int               login;            // owner's login
	char              symbol[12];       // security
	int               digits;           // security precision
	int               cmd;              // trade command
	int               volume;           // volume
	//---
	__time32_t        open_time;        // open time
	int               state;            // reserved
	double            open_price;       // open price
	double            sl, tp;            // stop loss & take profit
	__time32_t        close_time;       // close time
	int               gw_volume;        // gateway order volume
	__time32_t        expiration;       // pending order's expiration time
	char              reason;           // trade reason
	char              conv_reserv[3];   // reserved fields
	double            conv_rates[2];    // convertation rates from profit currency to group deposit currency
										// (first element-for open time, second element-for close time)
	double            commission;       // commission
	double            commission_agent; // agent commission
	double            storage;          // order swaps
	double            close_price;      // close price
	double            profit;           // profit
	double            taxes;            // taxes
	int               magic;            // special value used by client experts
	char              comment[32];      // comment
	int               gw_order;         // gateway order ticket
	int               activation;       // used by MT Manager
	short             gw_open_price;    // gateway order price deviation (pips) from order open price
	short             gw_close_price;   // gateway order price deviation (pips) from order close price
	double            margin_rate;      // margin convertation rate (rate of convertation from margin currency to deposit one)
	__time32_t        timestamp;        // timestamp
	int               api_data[4];      // for api usage
	TradeRecord* __ptr32 next;          // internal data
};
#pragma pack(pop)
//--- trade commands
enum { OP_BUY = 0, OP_SELL, OP_BUY_LIMIT, OP_SELL_LIMIT, OP_BUY_STOP, OP_SELL_STOP, OP_BALANCE, OP_CREDIT };
//--- trade record state
enum { TS_OPEN_NORMAL, TS_OPEN_REMAND, TS_OPEN_RESTORED, TS_CLOSED_NORMAL, TS_CLOSED_PART, TS_CLOSED_BY, TS_DELETED };
//--- trade record reasons
enum
{
	TR_REASON_CLIENT = 0,  // client terminal
	TR_REASON_EXPERT = 1,  // expert
	TR_REASON_DEALER = 2,  // dealer
	TR_REASON_SIGNAL = 3,  // signal
	TR_REASON_GATEWAY = 4,  // gateway
	TR_REASON_MOBILE = 5,  // mobile terminal
	TR_REASON_WEB = 6,  // Web terminal
	TR_REASON_API = 7,  // API
};
//--- activation types
//--- *_ROLLBACK=current price roll back from activation price level
enum
{
	//--- no activation
	ACTIVATION_NONE = 0,
	//--- stoploss, takeprofit, pendings
	ACTIVATION_SL, ACTIVATION_TP, ACTIVATION_PENDING,
	//--- stopout
	ACTIVATION_STOPOUT,
	//--- rollbacks
	ACTIVATION_SL_ROLLBACK = -ACTIVATION_SL,
	ACTIVATION_TP_ROLLBACK = -ACTIVATION_TP,
	ACTIVATION_PENDING_ROLLBACK = -ACTIVATION_PENDING,
	ACTIVATION_STOPOUT_ROLLBACK = -ACTIVATION_STOPOUT
};
//+------------------------------------------------------------------+
//| TradeRecord restoring from backup result                         |
//+------------------------------------------------------------------+
#pragma pack(push,1)
struct TradeRestoreResult
{
	int               order;            // order
	UCHAR             res;              // RET_OK    - order restored
										// RET_ERROR - error restoring order
};
#pragma pack(pop)
//+------------------------------------------------------------------+
//| Trade transaction                                                |
//+------------------------------------------------------------------+
#pragma pack(push,1)
struct TradeTransInfo
{
	UCHAR             type;             // trade transaction type
	char              flags;            // flags
	short             cmd;              // trade command
	int               order, orderby;    // order, order by
	char              symbol[12];       // trade symbol
	int               volume;           // trade volume
	double            price;            // trade price
	double            sl, tp;            // stoploss, takeprofit
	int               ie_deviation;     // deviation on IE
	char              comment[32];      // comment
	__time32_t        expiration;       // pending order expiration time
	int               crc;              // crc
};
#pragma pack(pop)
//--- trade transaction types
enum
{
	//---
	TT_PRICES_GET,                      // prices requets
	TT_PRICES_REQUOTE,                  // requote
	//--- client trade transaction
	TT_ORDER_IE_OPEN = 64,                // open order (Instant Execution)
	TT_ORDER_REQ_OPEN,                  // open order (Request Execution)
	TT_ORDER_MK_OPEN,                   // open order (Market Execution)
	TT_ORDER_PENDING_OPEN,              // open pending order
	//---
	TT_ORDER_IE_CLOSE,                  // close order (Instant Execution)
	TT_ORDER_REQ_CLOSE,                 // close order (Request Execution)
	TT_ORDER_MK_CLOSE,                  // close order (Market Execution)
	//---
	TT_ORDER_MODIFY,                    // modify pending order
	TT_ORDER_DELETE,                    // delete pending order
	TT_ORDER_CLOSE_BY,                  // close order by order
	TT_ORDER_CLOSE_ALL,                 // close all orders by symbol
	//--- broker trade transactions
	TT_BR_ORDER_OPEN,                   // open order
	TT_BR_ORDER_CLOSE,                  // close order
	TT_BR_ORDER_DELETE,                 // delete order (ANY OPEN ORDER!!!)
	TT_BR_ORDER_CLOSE_BY,               // close order by order
	TT_BR_ORDER_CLOSE_ALL,              // close all orders by symbol
	TT_BR_ORDER_MODIFY,                 // modify open price, stoploss, takeprofit etc. of order
	TT_BR_ORDER_ACTIVATE,               // activate pending order
	TT_BR_ORDER_COMMENT,                // modify comment of order
	TT_BR_BALANCE                       // balance/credit
};
//--- trade request flags
enum EnReqFlags
{
	TT_FLAG_NONE = 0x00000000,         // flags none
	TT_FLAG_SIGNAL = 0x00000001,         // placed by signal
	TT_FLAG_EXPERT = 0x00000002,         // placed by expert
	TT_FLAG_GATEWAY = 0x00000004,         // placed by gateway
	TT_FLAG_MOBILE = 0x00000008,         // placed by mobile terminal
	TT_FLAG_WEB = 0x00000010,         // placed by web terminal
	TT_FLAG_API = 0x00000020,         // placed by api
};
//+------------------------------------------------------------------+
//| Margin level of the user                                         |
//+------------------------------------------------------------------+
struct MarginLevel
{
	int               login;            // user login
	char              group[16];        // user group
	int               leverage;         // user leverage
	int               updated;          // (internal)
	double            balance;          // balance+credit
	double            equity;           // equity
	int               volume;           // lots
	double            margin;           // margin requirements
	double            margin_free;      // free margin
	double            margin_level;     // margin level
	int               margin_type;      // margin controlling type (percent/currency)
	int               level_type;       // level type(ok/margincall/stopout)
};
//--- margin level type
enum { MARGINLEVEL_OK = 0, MARGINLEVEL_MARGINCALL, MARGINLEVEL_STOPOUT };
//+------------------------------------------------------------------+
//| Trade request                                                    |
//+------------------------------------------------------------------+
struct RequestInfo
{
	//--- general props
	int               id;                  // request id
	char              status;              // request status
	DWORD             time;                // request time
	int               manager;             // manager processing request (if any)
	//--- user info
	int               login;               // user login
	char              group[16];           // user group
	double            balance;             // user balance
	double            credit;              // user credit
	//--- processing trade transaction
	double            prices[2];           // bid/ask
	TradeTransInfo    trade;               // trade transaction
	int               gw_volume;           // gateway order volume
	int               gw_order;            // gateway order ticket
	short             gw_price;            // gateway order price deviation (pips) from request price
	//---
	RequestInfo* __ptr32 prev;
	RequestInfo* __ptr32 next;             // (internal use)
};
//--- trade request status
enum { DC_EMPTY, DC_REQUEST, DC_LOCKED, DC_ANSWERED, DC_RESETED, DC_CANCELED };
//--- time conversion ratio
#define TIME_RATE         ((double)1.6777216)
//--- conversion from our time to standard __time32_t
#define STDTIME(custom_time) ((DWORD)((double)(custom_time)*TIME_RATE))
//--- conversion from standard __time32_t to our time
#define OURTIME(stdtime)     ((DWORD)((double)(stdtime)/TIME_RATE))
//--- request confirmation modes
enum EnConfirmModes
{
	CONFIRM_MODE_ADD_PRICES = 0x00000001,    // throw in prices
	CONFIRM_MODE_PACKET = 0x00000002     // multiple execution
};
//+------------------------------------------------------------------+
//| Daily report                                                     |
//+------------------------------------------------------------------+
struct DailyReport
{
	int               login;                // login
	__time32_t        ctm;                  // time
	char              group[16];            // group
	char              bank[64];             // bank
	double            balance_prev;         // previous balance
	double            balance;              // balance
	double            deposit;              // deposit
	double            credit;               // credit
	double            profit_closed;        // closed profit/loss
	double            profit;               // floating profit/loss
	double            equity;               // equity
	double            margin;               // used margin
	double            margin_free;          // free margin
	//---
	int               next;                 // (internal)
	int               reserved[3];          // reserved
};
//+------------------------------------------------------------------+
//| Reports request                                                  |
//+------------------------------------------------------------------+
#pragma pack(push,1)
struct ReportGroupRequest
{
	char              name[32];            // request group name
	__time32_t        from;                // from
	__time32_t        to;                  // to
	int               total;               // total logins in request group
};
#pragma pack(pop)
//+------------------------------------------------------------------+
//| Daily reports request                                            |
//+------------------------------------------------------------------+
struct DailyGroupRequest
{
	char              name[32];            // group name
	__time32_t        from;                // from
	__time32_t        to;                  // to
	int               total;               // total logins in request group
	int               reserved;            // reserved
};
//+------------------------------------------------------------------+
//| Selected symbol information                                      |
//+------------------------------------------------------------------+
struct SymbolInfo
{
	char              symbol[12];            // symbol name
	int               digits;                // floating point digits
	int               count;                 // symbol counter
	int               visible;               // visibility
	//---
	int               type;                  // symbol type (symbols group index)
	double            point;                 // symbol point=1/pow(10,digits)
	int               spread;                // symbol spread
	int               spread_balance;        // spread balance
	//---
	int               direction;             // direction
	int               updateflag;            // update flag
	__time32_t        lasttime;              // last tick time
	double            bid, ask;               // bid, ask
	double            high, low;              // high, low
	double            commission;            // commission
	int               comm_type;             // commission type
};
//--- symbol price direction
enum { SDIR_UP, SDIR_DOWN, SDIR_NONE };
//+------------------------------------------------------------------+
//| Symbol summary                                                   |
//+------------------------------------------------------------------+
struct SymbolSummary
{
	char              symbol[12];          // symbol
	int               count;               // symbol counter
	int               digits;              // floating point digits
	int               type;                // symbol type (symbol group index)
	//--- clients summary
	int               orders;              // number of client orders
	__int64           buylots;             // buy volume
	__int64           selllots;            // sell volume
	double            buyprice;            // average buy price
	double            sellprice;           // average sell price
	double            profit;              // clients profit
	//--- coverage summary
	int               covorders;           // number of coverage orders
	__int64           covbuylots;          // buy volume
	__int64           covselllots;         // sell volume
	double            covbuyprice;         // average buy price
	double            covsellprice;        // average sell price
	double            covprofit;           // coverage profit
};
//+------------------------------------------------------------------+
//| Currence exposure                                                |
//+------------------------------------------------------------------+
struct ExposureValue
{
	char              currency[4];         // currency
	double            clients;             // clients volume
	double            coverage;            // coverage volume
};
//+------------------------------------------------------------------+
//| Symbol properties                                                |
//+------------------------------------------------------------------+
#pragma pack(push,1)
struct SymbolPropertiesOld
{
	char              symbol[12];             // symbol
	COLORREF          color;                  // symbol color
	int               spread;                 // symbol spread
	int               spread_balance;         // spread balance
	int               stops_level;            // stops level
	int               exemode;                // execution mode
};
#pragma pack(pop)
//---
#pragma pack(push,1)
struct SymbolProperties
{
	char              symbol[12];            // symbol
	COLORREF          color;                 // symbol color
	int               spread;                // symbol spread
	int               spread_balance;        // spread balance
	int               stops_level;           // stops level
	int               smoothing;             // smoothing
	int               exemode;               // execution mode
	int               reserved[8];           // reserved
};
#pragma pack(pop)
//+------------------------------------------------------------------+
//| Symbol tick                                                      |
//+------------------------------------------------------------------+
struct TickInfo
{
	char              symbol[12];             // symbol
	__time32_t        ctm;                    // tick time
	double            bid;                    // bid
	double            ask;                    // ask
};
//+------------------------------------------------------------------+
//| Mail                                                             |
//+------------------------------------------------------------------+
struct MailBox
{
	__time32_t        time;                   // receive time
	int               sender;                 // mail sender (login)
	char              from[64];               // mail sender (name)
	int               to;                     // mail recipient
	char              subject[128];           // mail sumbect
	int               readed;                 // readed flag
	char* __ptr32     body;                   // pointer to mail body
	int               bodylen;                // mail body length
	short             build_min;              // minimum build
	short             build_max;              // maximum build
	int               reserved;               // reserved
};
//+------------------------------------------------------------------+
//| News topic                                                       |
//+------------------------------------------------------------------+
struct NewsTopic
{
	ULONG             key;                    // news key
	__time32_t        time;                   // news time
	char              ctm[32];                // news source time ("yyyy/mm/dd hh:mm:ss")
	char              topic[256];             // news topic
	char              category[64];           // news category
	char              keywords[256];          // news keywords
	char* __ptr32     body;                   // body (if present)
	int               bodylen;                // body length
	int               readed;                 // readed flag
	int               priority;               // news priority: 0-general, 1-high
	int               langid;                 // news LANGID
	int               reserved[1];
};
//+------------------------------------------------------------------+
//| Extended news structure                                          |
//+------------------------------------------------------------------+
#pragma pack(push,1)
struct NewsTopicNew
{
	//--- constants
	enum constants
	{
		MAX_NEWS_BODY_LEN = 15 * 1024 * 1024         // max. body len
	};
	//--- news topic flags
	enum EnNewsFlags
	{
		FLAG_PRIORITY = 1,                   // priority flag
		FLAG_CALENDAR = 2,                   // calendar item flag
		FLAG_MIME = 4,                   // MIME news content
		FLAG_ALLOW_DEMO = 8                    // allow body for demo accounts
	};
	ULONG             key;                    // news key
	UINT              language;               // news language (WinAPI LANGID)
	wchar_t           subject[256];           // news subject
	wchar_t           category[256];          // news category
	UINT              flags;                  // EnNewsFlags
	wchar_t* __ptr32  body;                   // body
	UINT              body_len;               // body length
	UINT              languages_list[32];     // list of languages available for news
	INT64             datetime;               // news time
	UINT              reserved[30];           // reserved
};
#pragma pack(pop)
//+------------------------------------------------------------------+
//| Server journal record                                            |
//+------------------------------------------------------------------+
struct ServerLog
{
	int               code;                   // code
	char              time[24];               // time
	char              ip[256];                // ip
	char              message[512];           // message
};
//--- log record codes
enum EnErrLogTypes {
	CmdOK,                  // OK
	CmdTrade,               // trades only
	CmdLogin,               // logins only
	CmdWarn,                // warnings
	CmdErr,                 // errors
	CmdAtt                  // attention, important errors
};
//--- request logs type
enum EnLogType {
	LOG_TYPE_STANDARD = 0,    // all except logins
	LOG_TYPE_LOGINS = 1,    // logins only
	LOG_TYPE_TRADES = 2,    // trades only
	LOG_TYPE_ERRORS = 3,    // errors
	LOG_TYPE_FULL = 4,    // full log
	//---
	LOG_TYPE_UPDATER = 16,   // live update
	LOG_TYPE_SENDMAIL = 17,   // send mail
	LOG_TYPE_FAILOVER = 18    // failover
};
//--- request logs type
enum EnLogMode {
	LOG_MODE_ENABLED,       // manager api logs enabled
	LOG_MODE_DISABLED,      // manager api logs disabled
};
//+------------------------------------------------------------------+
//| Balance check                                                    |
//+------------------------------------------------------------------+
#pragma pack(push,1)
struct BalanceDiff
{
	int               login;
	double            diff;
};
#pragma pack(pop)
//+------------------------------------------------------------------+
//| Pumping notification codes                                       |
//+------------------------------------------------------------------+
enum
{
	PUMP_START_PUMPING = 0,      // pumping started
	PUMP_UPDATE_SYMBOLS,       // update symbols
	PUMP_UPDATE_GROUPS,        // update groups
	PUMP_UPDATE_USERS,         // update users
	PUMP_UPDATE_ONLINE,        // update online users
	PUMP_UPDATE_BIDASK,        // update bid/ask
	PUMP_UPDATE_NEWS,          // update news
	PUMP_UPDATE_NEWS_BODY,     // update news body
	PUMP_UPDATE_MAIL,          // update news
	PUMP_UPDATE_TRADES,        // update trades
	PUMP_UPDATE_REQUESTS,      // update trade requests
	PUMP_UPDATE_PLUGINS,       // update server plugins
	PUMP_UPDATE_ACTIVATION,    // new order for activation (sl/sp/stopout)
	PUMP_UPDATE_MARGINCALL,    // new margin calls
	PUMP_STOP_PUMPING,         // pumping stopped
	PUMP_PING,                 // ping
	PUMP_UPDATE_NEWS_NEW,      // update news in new format (NewsTopicNew structure)
};
//+------------------------------------------------------------------+
//| Dealing notification codes                                       |
//+------------------------------------------------------------------+
enum
{
	DEAL_START_DEALING = 0,      // dealing dispatcher started
	DEAL_REQUEST_NEW,          // new request
	DEAL_STOP_DEALING          // dealing dispatcher stopped
};
//+------------------------------------------------------------------+
//| Notification callback function                                   |
//+------------------------------------------------------------------+
typedef void(__stdcall* MTAPI_NOTIFY_FUNC)(int code);
//+------------------------------------------------------------------+
//| Notification callback function                                   |
//+------------------------------------------------------------------+
typedef void(__stdcall* MTAPI_NOTIFY_FUNC_EX)(int code, int type, void* data, void* param);
//+------------------------------------------------------------------+
//| MetaTrader Manager Interface                                     |
//+------------------------------------------------------------------+
class CManagerInterface
{
public:
	//--- dummy methods for delphi
	virtual int    __stdcall QueryInterface(REFIID riid, LPVOID* obj) = 0;
	virtual int    __stdcall AddRef() = 0;
	//--- release
	virtual int    __stdcall Release() = 0;
	//--- service methods
	virtual void   __stdcall MemFree(void* ptr) = 0;
	virtual LPCSTR __stdcall ErrorDescription(const int code) = 0;
	virtual void   __stdcall WorkingDirectory(LPCSTR path) = 0;
	//--- connection
	virtual int  __stdcall Connect(LPCSTR server) = 0;
	virtual int  __stdcall Disconnect() = 0;
	virtual int  __stdcall IsConnected() = 0;
	virtual int  __stdcall Login(const int login, LPCSTR password) = 0;
	virtual int  __stdcall LoginSecured(LPCSTR key_path) = 0;
	virtual int  __stdcall KeysSend(LPCSTR key_path) = 0;
	virtual int  __stdcall Ping() = 0;
	virtual int  __stdcall PasswordChange(LPCSTR pass, const int is_investor) = 0;
	virtual int  __stdcall ManagerRights(ConManager* man) = 0;
	//--- server administration commands
	virtual int  __stdcall SrvRestart() = 0;
	virtual int  __stdcall SrvChartsSync() = 0;
	virtual int  __stdcall SrvLiveUpdateStart() = 0;
	virtual int  __stdcall SrvFeedsRestart() = 0;
	//--- server configuration
	   //--- configuration request
	virtual int  __stdcall CfgRequestCommon(ConCommon* cfg) = 0;
	virtual int  __stdcall CfgRequestTime(ConTime* cfg) = 0;
	virtual int  __stdcall CfgRequestBackup(ConBackup* cfg) = 0;
	virtual int  __stdcall CfgRequestSymbolGroup(ConSymbolGroup* cfg) = 0;
	virtual ConAccess* __stdcall CfgRequestAccess(int* total) = 0;
	virtual ConDataServer* __stdcall CfgRequestDataServer(int* total) = 0;
	virtual ConHoliday* __stdcall CfgRequestHoliday(int* total) = 0;
	virtual ConSymbol* __stdcall CfgRequestSymbol(int* total) = 0;
	virtual ConGroup* __stdcall CfgRequestGroup(int* total) = 0;
	virtual ConManager* __stdcall CfgRequestManager(int* total) = 0;
	virtual ConFeeder* __stdcall CfgRequestFeeder(int* total) = 0;
	virtual ConLiveUpdate* __stdcall CfgRequestLiveUpdate(int* total) = 0;
	virtual ConSync* __stdcall CfgRequestSync(int* total) = 0;
	virtual ConPluginParam* __stdcall CfgRequestPlugin(int* total) = 0;
	//--- configuration update
	virtual int  __stdcall CfgUpdateCommon(const ConCommon* cfg) = 0;
	virtual int  __stdcall CfgUpdateAccess(const ConAccess* cfg, const int pos) = 0;
	virtual int  __stdcall CfgUpdateDataServer(const ConDataServer* cfg, const int pos) = 0;
	virtual int  __stdcall CfgUpdateTime(const ConTime* cfg) = 0;
	virtual int  __stdcall CfgUpdateHoliday(const ConHoliday* cfg, const int pos) = 0;
	virtual int  __stdcall CfgUpdateSymbol(const ConSymbol* cfg) = 0;
	virtual int  __stdcall CfgUpdateSymbolGroup(const ConSymbolGroup* cfg, const int pos) = 0;
	virtual int  __stdcall CfgUpdateGroup(const ConGroup* cfg) = 0;
	virtual int  __stdcall CfgUpdateManager(const ConManager* cfg) = 0;
	virtual int  __stdcall CfgUpdateFeeder(const ConFeeder* cfg) = 0;
	virtual int  __stdcall CfgUpdateBackup(const ConBackup* cfg) = 0;
	virtual int  __stdcall CfgUpdateLiveUpdate(const ConLiveUpdate* cfg) = 0;
	virtual int  __stdcall CfgUpdateSync(const ConSync* cfg) = 0;
	virtual int  __stdcall CfgUpdatePlugin(const ConPlugin* cfg, const PluginCfg* parupd, const int total) = 0;
	//--- configuration delete
	virtual int  __stdcall CfgDeleteAccess(const int pos) = 0;
	virtual int  __stdcall CfgDeleteDataServer(const int pos) = 0;
	virtual int  __stdcall CfgDeleteHoliday(const int pos) = 0;
	virtual int  __stdcall CfgDeleteSymbol(const int pos) = 0;
	virtual int  __stdcall CfgDeleteGroup(const int pos) = 0;
	virtual int  __stdcall CfgDeleteManager(const int pos) = 0;
	virtual int  __stdcall CfgDeleteFeeder(const int pos) = 0;
	virtual int  __stdcall CfgDeleteLiveUpdate(const int pos) = 0;
	virtual int  __stdcall CfgDeleteSync(const int pos) = 0;
	//--- configuration shift
	virtual int  __stdcall CfgShiftAccess(const int pos, const int shift) = 0;
	virtual int  __stdcall CfgShiftDataServer(const int pos, const int shift) = 0;
	virtual int  __stdcall CfgShiftHoliday(const int pos, const int shift) = 0;
	virtual int  __stdcall CfgShiftSymbol(const int pos, const int shift) = 0;
	virtual int  __stdcall CfgShiftGroup(const int pos, const int shift) = 0;
	virtual int  __stdcall CfgShiftManager(const int pos, const int shift) = 0;
	virtual int  __stdcall CfgShiftFeeder(const int pos, const int shift) = 0;
	virtual int  __stdcall CfgShiftLiveUpdate(const int pos, const int shift) = 0;
	virtual int  __stdcall CfgShiftSync(const int pos, const int shift) = 0;
	virtual int  __stdcall CfgShiftPlugin(const int pos, const int shift) = 0;
	//--- server feeders
	virtual ServerFeed* __stdcall SrvFeeders(int* total) = 0;
	virtual LPSTR       __stdcall SrvFeederLog(LPCSTR name, int* len) = 0;
	//--- chart bases
	virtual RateInfoOld* __stdcall ChartRequestObsolete(const ChartInfo* chart, __time32_t* timesign, int* total) = 0;
	virtual int          __stdcall ChartAddObsolete(LPCSTR symbol, const int period, const RateInfoOld* rates, int* count) = 0;
	virtual int          __stdcall ChartUpdateObsolete(LPCSTR symbol, const int period, const RateInfoOld* rates, int* count) = 0;
	virtual int          __stdcall ChartDeleteObsolete(LPCSTR symbol, const int period, const RateInfoOld* rates, int* count) = 0;
	//--- performance info
	virtual PerformanceInfo* __stdcall PerformanceRequest(__time32_t from, int* total) = 0;
	//--- users/trades backups
	virtual BackupInfo* __stdcall BackupInfoUsers(const int mode, int* total) = 0;
	virtual BackupInfo* __stdcall BackupInfoOrders(const int mode, int* total) = 0;
	virtual UserRecord* __stdcall BackupRequestUsers(LPCSTR file, LPCSTR request, int* total) = 0;
	virtual TradeRecord* __stdcall BackupRequestOrders(LPCSTR file, LPCSTR request, int* total) = 0;
	virtual int          __stdcall BackupRestoreUsers(const UserRecord* users, const int total) = 0;
	virtual TradeRestoreResult* __stdcall BackupRestoreOrders(const TradeRecord* trades, int* total) = 0;
	//--- administrator databases commands
	virtual UserRecord* __stdcall AdmUsersRequest(LPCSTR group, int* total) = 0;
	virtual TradeRecord* __stdcall AdmTradesRequest(LPCSTR group, const int open_only, int* total) = 0;
	virtual int          __stdcall AdmBalanceCheckObsolete(int* logins, int* total) = 0;
	virtual int          __stdcall AdmBalanceFix(const int* logins, const int total) = 0;
	virtual int          __stdcall AdmTradesDelete(const int* orders, const int total) = 0;
	virtual int          __stdcall AdmTradeRecordModify(const TradeRecord* trade) = 0;
	//--- symbols
	virtual int          __stdcall SymbolsRefresh() = 0;
	virtual ConSymbol* __stdcall SymbolsGetAll(int* total) = 0;
	virtual int          __stdcall SymbolGet(LPCSTR symbol, ConSymbol* cs) = 0;
	virtual int          __stdcall SymbolInfoGet(LPCSTR symbol, SymbolInfo* si) = 0;
	virtual int          __stdcall SymbolAdd(LPCSTR symbol) = 0;
	virtual int          __stdcall SymbolHide(LPCSTR symbol) = 0;
	//--- symbol commands
	virtual int          __stdcall SymbolChangeObsolete(const SymbolPropertiesOld* prop) = 0;
	virtual int          __stdcall SymbolSendTick(LPCSTR symbol, const double bid, const double ask) = 0;
	//--- manager commands
	virtual ConGroup* __stdcall GroupsRequest(int* total) = 0;
	virtual int          __stdcall MailSend(const MailBox* mail, const int* logins) = 0;
	virtual int          __stdcall NewsSend(const NewsTopic* news) = 0;
	//--- journal
	virtual ServerLog* __stdcall JournalRequest(const int mode, const __time32_t from, const __time32_t to, LPCSTR filter, int* total) = 0;
	//--- databases: direct request to the server
	//--- users
	virtual UserRecord* __stdcall UsersRequest(int* total) = 0;
	virtual UserRecord* __stdcall UserRecordsRequest(const int* logins, int* total) = 0;
	virtual int          __stdcall UserRecordNew(UserRecord* user) = 0;
	virtual int          __stdcall UserRecordUpdate(const UserRecord* user) = 0;
	virtual int          __stdcall UsersGroupOp(const GroupCommandInfo* info, const int* logins) = 0;
	virtual int          __stdcall UserPasswordCheck(const int login, LPCSTR password) = 0;
	virtual int          __stdcall UserPasswordSet(const int login, LPCSTR password, const int change_investor, const int clean_pubkey) = 0;
	virtual OnlineRecord* __stdcall OnlineRequest(int* total) = 0;
	//--- orders
	virtual int          __stdcall TradeTransaction(TradeTransInfo* info) = 0;
	virtual TradeRecord* __stdcall TradesRequest(int* total) = 0;
	virtual TradeRecord* __stdcall TradeRecordsRequest(const int* orders, int* total) = 0;
	virtual TradeRecord* __stdcall TradesUserHistory(const int login, const __time32_t from, const __time32_t to, int* total) = 0;
	virtual int          __stdcall TradeCheckStops(const TradeTransInfo* trade, const double price) = 0;
	//--- reports
	virtual TradeRecord* __stdcall ReportsRequest(const ReportGroupRequest* req, const int* logins, int* total) = 0;
	virtual DailyReport* __stdcall DailyReportsRequest(const DailyGroupRequest* req, const int* logins, int* total) = 0;
	//--- external command
	virtual int          __stdcall ExternalCommand(LPCSTR data_in, const int size_in, LPSTR* data_out, int* size_out) = 0;
	//--- plugins
	virtual int          __stdcall PluginUpdate(const ConPluginParam* plugin) = 0;
	//--- pumping
	virtual int          __stdcall PumpingSwitch(MTAPI_NOTIFY_FUNC pfnFunc, const HWND destwnd, const UINT eventmsg, const int flags) = 0;
	virtual ConGroup* __stdcall GroupsGet(int* total) = 0;
	virtual int          __stdcall GroupRecordGet(LPCSTR name, ConGroup* group) = 0;
	virtual int          __stdcall SymbolInfoUpdated(SymbolInfo* si, const int max_info) = 0;
	virtual UserRecord* __stdcall UsersGet(int* total) = 0;
	virtual int          __stdcall UserRecordGet(const int login, UserRecord* user) = 0;
	virtual OnlineRecord* __stdcall OnlineGet(int* total) = 0;
	virtual int          __stdcall OnlineRecordGet(const int login, OnlineRecord* user) = 0;
	virtual TradeRecord* __stdcall TradesGet(int* total) = 0;
	virtual TradeRecord* __stdcall TradesGetBySymbol(LPCSTR symbol, int* total) = 0;
	virtual TradeRecord* __stdcall TradesGetByLogin(const int login, LPCSTR group, int* total) = 0;
	virtual TradeRecord* __stdcall TradesGetByMarket(int* total) = 0;
	virtual int          __stdcall TradeRecordGet(const int order, TradeRecord* trade) = 0;
	virtual int          __stdcall TradeClearRollback(const int order) = 0;
	virtual MarginLevel* __stdcall MarginsGet(int* total) = 0;
	virtual int          __stdcall MarginLevelGet(const int login, LPCSTR group, MarginLevel* margin) = 0;
	virtual RequestInfo* __stdcall RequestsGet(int* total) = 0;
	virtual int          __stdcall RequestInfoGet(const int pos, RequestInfo* info) = 0;
	virtual ConPlugin* __stdcall PluginsGet(int* total) = 0;
	virtual int          __stdcall PluginParamGet(const int pos, ConPluginParam* plugin) = 0;
	virtual int          __stdcall MailLast(LPSTR path, int* length) = 0;
	virtual NewsTopic* __stdcall NewsGet(int* total) = 0;
	virtual int          __stdcall NewsTotal() = 0;
	virtual int          __stdcall NewsTopicGet(const int pos, NewsTopic* news) = 0;
	virtual void         __stdcall NewsBodyRequest(const int key) = 0;
	virtual LPSTR        __stdcall NewsBodyGet(const int key) = 0;
	//--- dealing
	virtual int          __stdcall DealerSwitch(MTAPI_NOTIFY_FUNC pfnFunc, const HWND destwnd, const UINT eventmsg) = 0;
	virtual int          __stdcall DealerRequestGet(RequestInfo* info) = 0;
	virtual int          __stdcall DealerSend(const RequestInfo* info, const int requote, const int mode) = 0;
	virtual int          __stdcall DealerReject(const int id) = 0;
	virtual int          __stdcall DealerReset(const int id) = 0;
	//---
	virtual TickInfo* __stdcall TickInfoLast(LPCSTR symbol, int* total) = 0;
	virtual int          __stdcall SymbolsGroupsGet(ConSymbolGroup* grp) = 0;
	virtual __time32_t   __stdcall ServerTime() = 0;
	virtual MailBox* __stdcall MailsRequest(int* total) = 0;
	//--- risk management
	virtual SymbolSummary* __stdcall SummaryGetAll(int* total) = 0;
	virtual int          __stdcall SummaryGet(LPCSTR symbol, SymbolSummary* info) = 0;
	virtual int          __stdcall SummaryGetByCount(const int symbol, SymbolSummary* info) = 0;
	virtual int          __stdcall SummaryGetByType(const int sectype, SymbolSummary* info) = 0;
	virtual int          __stdcall SummaryCurrency(LPSTR cur, const int maxchars) = 0;
	virtual ExposureValue* __stdcall ExposureGet(int* total) = 0;
	virtual int          __stdcall ExposureValueGet(LPCSTR cur, ExposureValue* info) = 0;
	//---
	virtual int          __stdcall MarginLevelRequest(const int login, MarginLevel* level) = 0;
	virtual int          __stdcall HistoryCorrect(LPCSTR symbol, int* updated) = 0;
	//--- new chart bases
	virtual RateInfo* __stdcall ChartRequest(const ChartInfo* chart, __time32_t* timesign, int* total) = 0;
	virtual int          __stdcall ChartAdd(LPCSTR symbol, const int period, const RateInfo* rates, int* count) = 0;
	virtual int          __stdcall ChartUpdate(LPCSTR symbol, const int period, const RateInfo* rates, int* count) = 0;
	virtual int          __stdcall ChartDelete(LPCSTR symbol, const int period, const RateInfo* rates, int* count) = 0;
	//--- ticks base
	virtual TickRecord* __stdcall TicksRequest(const TickRequest* request, int* total) = 0;
	//--- internal methods
	virtual int          __stdcall PumpingSwitchEx(MTAPI_NOTIFY_FUNC_EX pfnFunc, const int flags, void* param) = 0;
	virtual int          __stdcall UsersSyncStart(const __time32_t timestamp) = 0;
	virtual UserRecord* __stdcall UsersSyncRead(int* users_total) = 0;
	virtual int* __stdcall UsersSnapshot(int* users_total) = 0;
	virtual int          __stdcall TradesSyncStart(const __time32_t timestamp) = 0;
	virtual TradeRecord* __stdcall TradesSyncRead(int* trades_total) = 0;
	virtual int* __stdcall TradesSnapshot(int* trades_total) = 0;
	virtual int          __stdcall DailySyncStart(const __time32_t timestamp) = 0;
	virtual DailyReport* __stdcall DailySyncRead(int* daily_total) = 0;
	//--- profit recalculation
	virtual int          __stdcall TradeCalcProfit(TradeRecord* trade) = 0;
	//--- new symbol commands
	virtual int          __stdcall SymbolChange(const SymbolProperties* prop) = 0;
	//--- network statistics
	virtual int          __stdcall BytesSent() = 0;
	virtual int          __stdcall BytesReceived() = 0;
	//---
	virtual int          __stdcall ManagerCommon(ConCommon* common) = 0;
	//--- log access
	virtual void         __stdcall LogsOut(const int code, LPCSTR source, LPCSTR msg) = 0;
	virtual void         __stdcall LogsMode(const int mode) = 0;
	//--- check license
	virtual int         __stdcall  LicenseCheck(LPCSTR license_name) = 0;
	//--- gateway configs
	virtual ConGatewayAccount* __stdcall CfgRequestGatewayAccount(int* total) = 0;
	virtual ConGatewayMarkup* __stdcall CfgRequestGatewayMarkup(int* total) = 0;
	virtual ConGatewayRule* __stdcall CfgRequestGatewayRule(int* total) = 0;
	//--- configuration update
	virtual int          __stdcall CfgUpdateGatewayAccount(const ConGatewayAccount* cfg) = 0;
	virtual int          __stdcall CfgUpdateGatewayMarkup(const ConGatewayMarkup* cfg) = 0;
	virtual int          __stdcall CfgUpdateGatewayRule(const ConGatewayRule* cfg) = 0;
	//--- configuration delete
	virtual int          __stdcall CfgDeleteGatewayAccount(const int pos) = 0;
	virtual int          __stdcall CfgDeleteGatewayMarkup(const int pos) = 0;
	virtual int          __stdcall CfgDeleteGatewayRule(const int pos) = 0;
	//--- configuration shift
	virtual int          __stdcall CfgShiftGatewayAccount(const int pos, const int shift) = 0;
	virtual int          __stdcall CfgShiftGatewayMarkup(const int pos, const int shift) = 0;
	virtual int          __stdcall CfgShiftGatewayRule(const int pos, const int shift) = 0;
	//--- administrator databases commands
	virtual BalanceDiff* __stdcall AdmBalanceCheck(int* logins, int* total) = 0;
	//--- notifications
	virtual int          __stdcall NotificationsSend(LPWSTR metaquotes_ids, LPCWSTR message) = 0;
	virtual int          __stdcall NotificationsSend(const int* logins, const UINT logins_total, LPCWSTR message) = 0;
};
//+------------------------------------------------------------------+
//| Functions                                                        |
//+------------------------------------------------------------------+
typedef int (*MtManVersion_t)(void);
typedef int (*MtManCreate_t)(int version, CManagerInterface** man);
//+------------------------------------------------------------------+
//| Factory                                                          |
//+------------------------------------------------------------------+
#ifndef _MT4MANDLL
class CManagerFactory
{
private:
	HMODULE           m_lib;
	MtManVersion_t    m_pfnManVersion;
	MtManCreate_t     m_pfnManCreate;
public:
	//--- constructor
	CManagerFactory(LPCSTR lib_path = NULL) :m_lib(NULL)
	{
		Init(lib_path);
	}
	//--- destructor
	~CManagerFactory()
	{
		if (m_lib)
		{
			m_pfnManVersion = NULL;
			m_pfnManCreate = NULL;
			::FreeLibrary(m_lib);
			m_lib = NULL;
		}
	}
	//--- initialization
	inline void Init(LPCSTR lib_path = NULL)
	{
		char path[256] = "";
		//---
		if (lib_path != NULL)
		{
			strcpy_s(path, lib_path);
			path[sizeof(path) - 1] = 0;
		}
		else
		{
#ifndef _WIN64
			strcpy_s(path, "mtmanapi.dll");
			path[sizeof(path) - 1] = 0;
#else
			strcpy_s(path, "mtmanapi64.dll");
			path[sizeof(path) - 1] = 0;
#endif
		}
		//---
		if (m_lib)
			::FreeLibrary(m_lib);
		if ((m_lib = ::LoadLibraryA(path)) != NULL)
		{
			m_pfnManVersion = reinterpret_cast<MtManVersion_t>(::GetProcAddress(m_lib, "MtManVersion"));
			m_pfnManCreate = reinterpret_cast<MtManCreate_t>(::GetProcAddress(m_lib, "MtManCreate"));
		}
		else
		{
			m_pfnManVersion = NULL;
			m_pfnManCreate = NULL;
		}
		//---
	}
	//--- winsock startup/cleanup
	inline int WinsockStartup() const
	{
		WSADATA wsa;
		return(WSAStartup(0x0202, &wsa) != 0 ? RET_ERROR : RET_OK);
	}
	inline void WinsockCleanup() const
	{
		WSACleanup();
	}
	//---
	inline int IsValid() const
	{
		return(m_lib != NULL && m_pfnManVersion != NULL && m_pfnManCreate != NULL) ? TRUE : FALSE;
	}
	inline int Version() const
	{
		return(m_pfnManVersion ? (*m_pfnManVersion)() : 0);
	}
	inline CManagerInterface* Create(const int version) const
	{
		CManagerInterface* man = NULL;
		if (m_pfnManCreate) (*m_pfnManCreate)(version, &man);
		return(man);
	}
};
#endif
//+------------------------------------------------------------------+
