# MT4 Manager API (Build 1260) 函数列表

## CManagerInterface 接口函数

### 基础接口函数
| 函数名 | 中文说明 |
|--------|----------|
| `QueryInterface(REFIID riid, LPVOID* obj)` | 查询接口 |
| `AddRef()` | 增加引用计数 |
| `Release()` | 释放引用 |
| `MemFree(void* ptr)` | 释放内存 |
| `ErrorDescription(const int code)` | 获取错误描述 |
| `WorkingDirectory(LPCSTR path)` | 设置工作目录 |

### 连接管理
| 函数名 | 中文说明 |
|--------|----------|
| `Connect(LPCSTR server)` | 连接服务器 |
| `Disconnect()` | 断开连接 |
| `IsConnected()` | 检查连接状态 |
| `Login(const int login, LPCSTR password)` | 登录 |
| `LoginSecured(LPCSTR key_path)` | 安全登录 |
| `KeysSend(LPCSTR key_path)` | 发送密钥 |
| `Ping()` | 心跳检测 |
| `PasswordChange(LPCSTR pass, const int is_investor)` | 修改密码 |
| `ManagerRights(ConManager* man)` | 获取管理员权限 |

### 配置管理 - 通用配置
| 函数名 | 中文说明 |
|--------|----------|
| `CfgRequestCommon()` | 请求通用配置 |
| `CfgUpdateCommon(const ConCommon* cfg)` | 更新通用配置 |

### 配置管理 - 访问权限
| 函数名 | 中文说明 |
|--------|----------|
| `CfgRequestAccess(int* total)` | 请求访问权限配置 |
| `CfgUpdateAccess(const ConAccess* cfg, const int pos)` | 更新访问权限 |
| `CfgDeleteAccess(const int pos)` | 删除访问权限 |
| `CfgShiftAccess(const int pos, const int shift)` | 移动访问权限位置 |

### 配置管理 - 数据服务器
| 函数名 | 中文说明 |
|--------|----------|
| `CfgRequestDataServer(int* total)` | 请求数据服务器配置 |
| `CfgUpdateDataServer(const ConDataServer* cfg, const int pos)` | 更新数据服务器 |
| `CfgDeleteDataServer(const int pos)` | 删除数据服务器 |
| `CfgShiftDataServer(const int pos, const int shift)` | 移动数据服务器位置 |

### 配置管理 - 实时配置
| 函数名 | 中文说明 |
|--------|----------|
| `CfgRequestTime()` | 请求时间配置 |
| `CfgUpdateTime(const ConTime* cfg)` | 更新时间配置 |

### 配置管理 - 节假日配置
| 函数名 | 中文说明 |
|--------|----------|
| `CfgRequestHoliday(int* total)` | 请求节假日配置 |
| `CfgUpdateHoliday(const ConHoliday* cfg, const int pos)` | 更新节假日 |
| `CfgDeleteHoliday(const int pos)` | 删除节假日 |
| `CfgShiftHoliday(const int pos, const int shift)` | 移动节假日位置 |

### 配置管理 - 交易品种
| 函数名 | 中文说明 |
|--------|----------|
| `CfgRequestSymbol(int* total)` | 请求交易品种配置 |
| `CfgUpdateSymbol(const ConSymbol* cfg)` | 更新交易品种 |
| `CfgDeleteSymbol(const int pos)` | 删除交易品种 |
| `CfgShiftSymbol(const int pos, const int shift)` | 移动交易品种位置 |

### 配置管理 - 交易组
| 函数名 | 中文说明 |
|--------|----------|
| `CfgRequestGroup(int* total)` | 请求交易组配置 |
| `CfgUpdateGroup(const ConGroup* cfg)` | 更新交易组 |
| `CfgDeleteGroup(const int pos)` | 删除交易组 |
| `CfgShiftGroup(const int pos, const int shift)` | 移动交易组位置 |

### 配置管理 - 管理员
| 函数名 | 中文说明 |
|--------|----------|
| `CfgRequestManager(int* total)` | 请求管理员配置 |
| `CfgUpdateManager(const ConManager* cfg)` | 更新管理员 |
| `CfgDeleteManager(const int pos)` | 删除管理员 |
| `CfgShiftManager(const int pos, const int shift)` | 移动管理员位置 |

### 配置管理 - 数据源
| 函数名 | 中文说明 |
|--------|----------|
| `CfgRequestFeeder(int* total)` | 请求数据源配置 |
| `CfgUpdateFeeder(const ConFeeder* cfg)` | 更新数据源 |
| `CfgDeleteFeeder(const int pos)` | 删除数据源 |
| `CfgShiftFeeder(const int pos, const int shift)` | 移动数据源位置 |

### 配置管理 - 备份
| 函数名 | 中文说明 |
|--------|----------|
| `CfgRequestBackup()` | 请求备份配置 |
| `CfgUpdateBackup(const ConBackup* cfg)` | 更新备份配置 |

### 配置管理 - 实时同步
| 函数名 | 中文说明 |
|--------|----------|
| `CfgRequestLiveUpdate(int* total)` | 请求实时更新配置 |
| `CfgUpdateLiveUpdate(const ConLiveUpdate* cfg)` | 更新实时更新配置 |

### 配置管理 - 同步
| 函数名 | 中文说明 |
|--------|----------|
| `CfgRequestSync()` | 请求同步配置 |
| `CfgUpdateSync(const ConSync* cfg)` | 更新同步配置 |

### 配置管理 - 插件
| 函数名 | 中文说明 |
|--------|----------|
| `CfgRequestPlugin(int* total)` | 请求插件配置 |
| `CfgUpdatePlugin(const ConPlugin* cfg, PluginCfg* parupd, const int total)` | 更新插件配置 |
| `CfgDeletePlugin(const int pos)` | 删除插件 |
| `CfgShiftPlugin(const int pos, const int shift)` | 移动插件位置 |

### 性能监控
| 函数名 | 中文说明 |
|--------|----------|
| `PerformanceRequest(const time_t from, PerformanceInfo* array, const int max_total)` | 请求性能数据 |

### 用户管理
| 函数名 | 中文说明 |
|--------|----------|
| `UsersRequest(int* total)` | 请求用户列表 |
| `UserRecordsRequest(const int* logins, const int total)` | 请求指定用户记录 |
| `UserRecordNew(const UserRecord* user)` | 创建新用户 |
| `UserRecordUpdate(const UserRecord* user)` | 更新用户记录 |
| `UsersGroupOp(const GroupCommandInfo* info, const int logins[])` | 用户组操作 |
| `UserPasswordCheck(const int login, LPCSTR password)` | 检查用户密码 |
| `UserPasswordSet(const int login, LPCSTR password, const int change_investor, const int clean_pubkey)` | 设置用户密码 |

### 用户在线管理
| 函数名 | 中文说明 |
|--------|----------|
| `OnlineRequest(int* total)` | 请求在线用户列表 |
| `OnlineRecordUpdate(const OnlineRecord* user)` | 更新在线用户记录 |

### 交易管理
| 函数名 | 中文说明 |
|--------|----------|
| `TradeTransaction(TradeTransInfo* info)` | 执行交易事务 |
| `TradesRequest(int* total)` | 请求交易列表 |
| `TradeRecordsRequest(const int* orders, const int total)` | 请求指定交易记录 |
| `TradeClearRollback(const int order)` | 清除交易回滚 |

### 交易历史
| 函数名 | 中文说明 |
|--------|----------|
| `TradesUserHistory(const int login, const time_t from, const time_t to, int* total)` | 获取用户交易历史 |
| `TradesCheckStops(const TradeRecord* trade, double* profit)` | 检查止损止盈 |

### 报表管理
| 函数名 | 中文说明 |
|--------|----------|
| `ReportsRequest(const ReportGroupRequest* req, const int* logins, const int total)` | 请求报表 |
| `DailyReportsRequest(const DailyGroupRequest* req, const int* logins, const int total)` | 请求日报表 |

### 账户管理
| 函数名 | 中文说明 |
|--------|----------|
| `UserHistoryGet(const int login, const time_t from, const time_t to, UserRecord* user)` | 获取用户历史 |
| `UserHistoryGetOld(const int login, const time_t ctm, UserRecord* user)` | 获取旧用户历史 |

### 图表数据
| 函数名 | 中文说明 |
|--------|----------|
| `ChartRequest(const ChartInfo* chart, const time_t from, const time_t to, int* total)` | 请求图表数据 |
| `ChartAdd(LPCSTR symbol, const int period, const RateInfo* rates, int* total, const int flags)` | 添加图表数据 |
| `ChartUpdate(LPCSTR symbol, const int period, const RateInfo* rates, int* total, const int flags)` | 更新图表数据 |
| `ChartDelete(LPCSTR symbol, const int period, const RateInfo* rates, const int total)` | 删除图表数据 |

### Tick数据
| 函数名 | 中文说明 |
|--------|----------|
| `TicksRequest(const TickRequest* req, const time_t from, const time_t to, const int flags)` | 请求Tick数据 |
| `TicksApply(LPCSTR symbol, TickRecord* ticks, int* total, const int flags)` | 应用Tick数据 |
| `TickRecordGet(LPCSTR symbol, const time_t ctm, const int flags, TickRecord* tick)` | 获取Tick记录 |

### 邮件发送
| 函数名 | 中文说明 |
|--------|----------|
| `MailSend(MailBox* mail, const int* logins)` | 发送邮件 |

### 新闻管理
| 函数名 | 中文说明 |
|--------|----------|
| `NewsSend(NewsTopic* news)` | 发送新闻 |

### 日志管理
| 函数名 | 中文说明 |
|--------|----------|
| `JournalRequest(const int mode, const time_t from, const time_t to, LPCSTR filter, int* total)` | 请求日志 |
| `LogsRequest(const LogRequest* request, int* total)` | 请求系统日志 |
| `LogsGet(const int pos, LPCSTR* log)` | 获取日志内容 |

### 泵送模式 (实时数据推送)
| 函数名 | 中文说明 |
|--------|----------|
| `PumpingSwitch(HWND hWnd, const UINT flags, LPCSTR server, const int reserved)` | 切换泵送模式 |
| `PumpingSwitchEx(const UINT flags, const int reserved)` | 扩展泵送模式切换 |
| `PumpingFunc(MTAPI_NOTIFY_FUNC pfnFunc, const void* destwnd, const UINT flags)` | 设置泵送回调函数 |
| `PumpingFuncEx(MTAPI_NOTIFY_FUNC_EX pfnFunc, const void* destwnd, const UINT flags)` | 设置扩展泵送回调 |
| `PumpingGetLock()` | 获取泵送锁 |
| `PumpingReleaseLock()` | 释放泵送锁 |
| `PumpingDataLock(const UINT type)` | 锁定泵送数据 |
| `PumpingDataUnlock(const UINT type)` | 解锁泵送数据 |
| `PumpingDataTotal(const UINT type)` | 获取泵送数据总量 |
| `PumpingDataNext(const UINT type, const int pos)` | 获取下一个泵送数据 |

### 品种管理
| 函数名 | 中文说明 |
|--------|----------|
| `SymbolsRefresh()` | 刷新品种列表 |
| `SymbolsGetAll(ConSymbol** symbols)` | 获取所有品种 |
| `SymbolGet(LPCSTR symbol, ConSymbol* cs)` | 获取指定品种 |
| `SymbolInfoGet(LPCSTR symbol, SymbolInfo* si)` | 获取品种信息 |
| `SymbolSendTick(LPCSTR symbol, double bid, double ask)` | 发送品种报价 |
| `SymbolsGroupsGet(ConSymbolGroup** sgs)` | 获取品种组 |
| `MarginLevelGet(const int login, LPCSTR group, MarginLevel* level)` | 获取保证金水平 |
| `MarginLevelSet(const int login, const MarginLevel* level)` | 设置保证金水平 |

### 处理者模式 (交易处理)
| 函数名 | 中文说明 |
|--------|----------|
| `DealerSwitch(HWND hWnd, const UINT flags, LPCSTR server, const int reserved)` | 切换处理者模式 |
| `DealerSwitchEx(const UINT flags, const int reserved)` | 扩展处理者模式切换 |
| `DealerRequestGet(RequestInfo* info)` | 获取交易请求 |
| `DealerSend(const RequestInfo* info, const int requote, const int mode)` | 发送交易处理结果 |
| `DealerReject(const int id)` | 拒绝交易请求 |
| `DealerReset(const int id)` | 重置交易请求 |

### 备份恢复
| 函数名 | 中文说明 |
|--------|----------|
| `BackupInfoUsers(const int* logins, BackupInfo** info, int* total)` | 备份用户信息 |
| `BackupInfoOrders(const int* logins, const int orders[], BackupInfo** info, int* total)` | 备份订单信息 |
| `BackupRequestUsers(const int* logins, const time_t from, const time_t to, int* total)` | 请求用户备份 |
| `BackupRequestOrders(const int* logins, const int orders[], const time_t from, const time_t to, int* total)` | 请求订单备份 |
| `BackupRestoreUsers(const int* logins, const BackupInfo* info)` | 恢复用户 |
| `BackupRestoreOrders(const int* logins, const int orders[], const BackupInfo* info)` | 恢复订单 |

### 管理员相关
| 函数名 | 中文说明 |
|--------|----------|
| `AdmUsersGet(const LPCSTR group, UserRecord** users, int* total)` | 获取管理员用户 |
| `AdmTradesGet(const LPCSTR group, const LPCSTR symbol, TradeRecord** trades, int* total)` | 获取管理员交易 |
| `AdmBalanceCheck(int* logins, int* total)` | 检查管理员余额 |

### 通知推送
| 函数名 | 中文说明 |
|--------|----------|
| `NotificationsSend(LPWSTR metaquotes_ids, LPCWSTR message)` | 发送通知(按ID) |
| `NotificationsSend(const int* logins, const UINT logins_total, LPCWSTR message)` | 发送通知(按登录号) |

## 主要数据结构

### 服务器配置相关
| 结构体名 | 说明 |
|----------|------|
| `ConCommon` | 通用服务器配置 |
| `ConAccess` | 访问权限配置 |
| `ConDataServer` | 数据服务器配置 |
| `ConTime` | 时间配置 |
| `ConHoliday` | 节假日配置 |
| `ConBackup` | 备份配置 |
| `ConSync` | 同步配置 |
| `ConLiveUpdate` | 实时更新配置 |

### 交易相关
| 结构体名 | 说明 |
|----------|------|
| `ConSymbol` | 交易品种配置 |
| `ConSymbolGroup` | 品种组配置 |
| `ConGroup` | 交易组配置 |
| `ConManager` | 管理员配置 |
| `ConFeeder` | 数据源配置 |
| `ConPlugin` | 插件配置 |
| `PluginCfg` | 插件参数配置 |

### 用户与交易记录
| 结构体名 | 说明 |
|----------|------|
| `UserRecord` | 用户记录 |
| `OnlineRecord` | 在线用户记录 |
| `TradeRecord` | 交易记录 |
| `TradeTransInfo` | 交易事务信息 |
| `RequestInfo` | 交易请求信息 |
| `MarginLevel` | 保证金水平 |

### 市场数据
| 结构体名 | 说明 |
|----------|------|
| `SymbolInfo` | 品种信息 |
| `RateInfo` | K线数据 |
| `ChartInfo` | 图表信息 |
| `TickRecord` | Tick数据 |
| `TickRequest` | Tick请求 |

### 报表与日志
| 结构体名 | 说明 |
|----------|------|
| `ReportGroupRequest` | 报表请求 |
| `DailyGroupRequest` | 日报表请求 |
| `LogRequest` | 日志请求 |
| `PerformanceInfo` | 性能信息 |

### 通讯相关
| 结构体名 | 说明 |
|----------|------|
| `MailBox` | 邮件信息 |
| `NewsTopic` | 新闻主题 |
| `BackupInfo` | 备份信息 |
| `BalanceDiff` | 余额差异 |
| `GroupCommandInfo` | 组命令信息 |

## 回调函数类型
| 类型定义 | 说明 |
|----------|------|
| `MTAPI_NOTIFY_FUNC` | 泵送模式通知回调 |
| `MTAPI_NOTIFY_FUNC_EX` | 扩展泵送模式通知回调 |

## DLL信息
- **32位版本**: mtmanapi.dll
- **64位版本**: mtmanapi64.dll
- **Build版本**: 1260 (2020年1月24日发布)

## 注意事项
1. 该API需要从MetaQuotes或白标供应商获得授权才能使用
2. 只有管理员组账户才能连接使用Manager API
3. API提供三种连接模式：
   - Normal模式（请求/响应）
   - Pumping模式（实时数据推送）
   - Dealing模式（交易处理）
4. Build 1260主要包含错误修复和稳定性改进